import 'package:Toooony/components/app_bar.dart';
import 'package:Toooony/theme/theme.dart';
import 'package:flutter/material.dart';

class AccountSecurityPage extends StatefulWidget {
  const AccountSecurityPage({super.key});

  @override
  State<AccountSecurityPage> createState() => _AccountSecurityPageState();
}

class _AccountSecurityPageState extends State<AccountSecurityPage> {
  @override
  Widget build(BuildContext context) {
    final theme = TonyTheme.of(context);
    return Scaffold(
      backgroundColor: theme.background,
      body: SafeArea(
        child: Column(
          children: [
            const TonyAppBar(
              middle: '账号与安全',
            ),
            const SizedBox(
              height: 20,
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 15),
              margin: const EdgeInsets.symmetric(
                horizontal: 15,
              ),
              decoration: BoxDecoration(
                color: theme.cardBackground,
                borderRadius: BorderRadius.circular(12),
              ),
              child: <PERSON>umn(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 15,
                    ),
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: theme.background,
                          width: 1.5,
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        Text(
                          '手机号',
                          style: TextStyle(
                            color: theme.onBackground,
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        const Spacer(),
                        Text(
                          '18699999999',
                          style: TextStyle(
                            color: theme.secondary,
                            fontSize: 14,
                            fontFamily: 'D-DIN-PRO',
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Image.asset(
                          'assets/Right.png',
                          width: 24,
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 15,
                    ),
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: theme.background,
                          width: 1.5,
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        Text(
                          '微信',
                          style: TextStyle(
                            color: theme.onBackground,
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        const Spacer(),
                        Text(
                          'AAX',
                          style: TextStyle(
                            color: theme.secondary,
                            fontSize: 14,
                            fontFamily: 'D-DIN-PRO',
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 15,
                    ),
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: theme.background,
                          width: 1.5,
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        Text(
                          '修改登录密码',
                          style: TextStyle(
                            color: theme.onBackground,
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        const Spacer(),
                        Image.asset(
                          'assets/Right.png',
                          width: 24,
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 15,
                    ),
                    child: Row(
                      children: [
                        Text(
                          '微信小程序',
                          style: TextStyle(
                            color: theme.onBackground,
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        const Spacer(),
                        Image.asset(
                          'assets/Right.png',
                          width: 24,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
