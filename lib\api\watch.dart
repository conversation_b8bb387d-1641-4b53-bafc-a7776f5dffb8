import 'package:Toooony/api/index.dart';
import 'package:Toooony/model/watch.dart';

class WatchApi {
  final Api api;

  WatchApi(this.api);

  Future<Device> getDeviceInfo(int deviceID) => api
      .get('/device/info/$deviceID')
      .then((res) => Device.fromJson(res.body));

  Future<Playbook> getPlaybook(int deviceID) => api
      .get('/device/$deviceID/playbook')
      .then((res) => Playbook.fromJson(res.body));

  Future<Playbook> updatePlaybook(int deviceID, Playbook playbook) =>
      api.put('/device/$deviceID/playbook', {
        'dashboardIDs': playbook.playInfo?.dashboardIDs,
      }).then((res) => Playbook.fromJson(res.body));

  Future<List<WatchTemplate>> getTemplates(int skip, int size) =>
      api.get('/dashboard/template/list', query: {
        'skip': skip.toString(),
        'size': size.toString()
      }).then((res) => (res.body as List<dynamic>)
          .map((item) => WatchTemplate.fromJson(item))
          .toList());

  Future<WatchInstance> getWatchInstanceInfo(int instanceID) => api
      .get('/dashboard/$instanceID')
      .then((res) => WatchInstance.fromJson(res.body));

  Future<List<WatchInstance>> getInstances() => api
      .get(
        '/dashboard/instances',
      )
      .then((res) => (res.body as List<dynamic>)
          .map((item) => WatchInstance.fromJson(item))
          .toList());

  Future<List<Device>> getDevices() =>
      api.get('/device/list').then((res) => (res.body as List<dynamic>)
          .map((item) => Device.fromJson(item))
          .toList());

  Future<void> unbindDevice(int deviceID) =>
      api.post('/device/$deviceID/unbind', {});

  Future<Device?> getCurrentDevice() => api
      .get('/device/current')
      .then((res) => res.body != null ? Device.fromJson(res.body) : null);

  Future<WatchInstance> createInstanceFromTemplate({
    required int templateID,
    String? name,
    String? description,
    String? cover,
    List<CustomField> customFields = const [],
  }) =>
      api.post('/dashboard/instance', {
        'templateID': templateID,
        'cover': cover,
        'customization': {
          if (name != null) 'name': name,
          if (description != null) 'description': description,
          if (cover != null) 'cover': cover,
          'customFields': customFields.map((field) => field.toJson()).toList()
        }
      }).then((res) => WatchInstance.fromJson(res.body));

  Future<List<WatchTag>> getWatchTags() => api
      .get('/dashboard/template/tags')
      .then((res) => (res.body as List<dynamic>)
          .map((item) => WatchTag.fromJson(item))
          .toList());

  Future<void> deleteInstance(int id) =>
      api.post('/dashboard/instances/$id/delete', {});

  Future<void> deleteInstances(List<int> ids) =>
      api.post('/dashboard/instances/delete', {'instanceIDs': ids});
}
