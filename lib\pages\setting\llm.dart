import 'package:Toooony/components/app_bar.dart';
import 'package:Toooony/theme/theme.dart';
import 'package:flutter/material.dart';

class LLMSettingPage extends StatefulWidget {
  const LLMSettingPage({super.key});

  @override
  State<LLMSettingPage> createState() => _LLMSettingPageState();
}

class _LLMSettingPageState extends State<LLMSettingPage> {
  @override
  Widget build(BuildContext context) {
    final theme = TonyTheme.of(context);
    return Scaffold(
      backgroundColor: theme.background,
      body: SafeArea(
        child: Column(
          children: [
            const TonyAppBar(
              middle: '大模型',
            ),
            const SizedBox(
              height: 20,
            ),
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 15),
              decoration: BoxDecoration(
                color: theme.cardBackground,
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 15),
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 15),
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          width: 1.5,
                          color: theme.background,
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        Text(
                          'ChatGPT4o',
                          style: TextStyle(
                            color: theme.onBackground,
                            fontSize: 14,
                          ),
                        ),
                        const Spacer(),
                        Image.asset(
                          'assets/Check-small.png',
                          width: 24,
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 15),
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          width: 1.5,
                          color: theme.background,
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        Text(
                          '文心一言',
                          style: TextStyle(
                            color: theme.onBackground,
                            fontSize: 14,
                          ),
                        ),
                        const Spacer(),
                        Image.asset(
                          'assets/Check-small.png',
                          width: 24,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
