// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'watch.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WebviewLoadingInfo _$WebviewLoadingInfoFromJson(Map<String, dynamic> json) =>
    WebviewLoadingInfo(
      loading: json['loading'] as bool? ?? false,
    );

Map<String, dynamic> _$WebviewLoadingInfoToJson(WebviewLoadingInfo instance) =>
    <String, dynamic>{
      'loading': instance.loading,
    };

WatchTemplate _$WatchTemplateFromJson(Map<String, dynamic> json) =>
    WatchTemplate(
      id: (json['id'] as num).toInt(),
      type: $enumDecodeNullable(_$WatchTypeEnumMap, json['type']) ??
          WatchType.Official,
      name: json['name'] as String? ?? '',
      version: json['version'] as String? ?? '',
      downloadSource: json['downloadSource'] as String? ?? '',
      description: json['description'] as String? ?? '',
      cover: json['cover'] as String? ?? '',
      customFields: (json['customFields'] as List<dynamic>?)
              ?.map((e) => CustomField.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$WatchTemplateToJson(WatchTemplate instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'version': instance.version,
      'description': instance.description,
      'downloadSource': instance.downloadSource,
      'cover': instance.cover,
      'customFields': instance.customFields,
      'type': _$WatchTypeEnumMap[instance.type]!,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$WatchTypeEnumMap = {
  WatchType.Official: 'Official',
  WatchType.ImageBackground: 'ImageBackground',
  WatchType.AI: 'AI',
  WatchType.Custom: 'Custom',
  WatchType.URL: 'URL',
};

WatchInstance _$WatchInstanceFromJson(Map<String, dynamic> json) =>
    WatchInstance(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      description: json['description'] as String,
      type: $enumDecode(_$DashTypeEnumMap, json['type']),
      cover: json['cover'] as String,
      size: (json['size'] as num).toInt(),
      customFields: (json['customFields'] as List<dynamic>?)
              ?.map((e) => CustomField.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      template:
          WatchTemplate.fromJson(json['template'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$WatchInstanceToJson(WatchInstance instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'cover': instance.cover,
      'size': instance.size,
      'type': _$DashTypeEnumMap[instance.type]!,
      'customFields': instance.customFields,
      'template': instance.template,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$DashTypeEnumMap = {
  DashType.ImageBackground: 'ImageBackground',
  DashType.AI: 'AI',
  DashType.Custom: 'Custom',
  DashType.Dash3D: 'Dash3D',
  DashType.URL: 'URL',
  DashType.Official: 'Official',
};

Playinfo _$PlayinfoFromJson(Map<String, dynamic> json) => Playinfo(
      dashboardIDs: (json['dashboardIDs'] as List<dynamic>)
          .map((e) => (e as num).toInt())
          .toList(),
    );

Map<String, dynamic> _$PlayinfoToJson(Playinfo instance) => <String, dynamic>{
      'dashboardIDs': instance.dashboardIDs,
    };

Playbook _$PlaybookFromJson(Map<String, dynamic> json) => Playbook(
      id: (json['id'] as num).toInt(),
      userID: (json['userID'] as num).toInt(),
      deviceID: (json['deviceID'] as num).toInt(),
      playInfo: json['playInfo'] == null
          ? null
          : Playinfo.fromJson(json['playInfo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PlaybookToJson(Playbook instance) => <String, dynamic>{
      'id': instance.id,
      'userID': instance.userID,
      'deviceID': instance.deviceID,
      'playInfo': instance.playInfo,
    };

Device _$DeviceFromJson(Map<String, dynamic> json) => Device(
      id: (json['id'] as num).toInt(),
      serial: json['serial'] as String? ?? '',
      encryptKey: json['encryptKey'] as String? ?? '',
      status: $enumDecodeNullable(_$EnumRobotStatusEnumMap, json['status']) ??
          EnumRobotStatus.ACTIVE,
      lastOnline: json['lastOnline'] == null
          ? null
          : DateTime.parse(json['lastOnline'] as String),
      currentDashboardID: (json['currentDashboardID'] as num?)?.toInt() ?? -1,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$DeviceToJson(Device instance) => <String, dynamic>{
      'id': instance.id,
      'serial': instance.serial,
      'encryptKey': instance.encryptKey,
      'currentDashboardID': instance.currentDashboardID,
      'status': _$EnumRobotStatusEnumMap[instance.status]!,
      'lastOnline': instance.lastOnline?.toIso8601String(),
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$EnumRobotStatusEnumMap = {
  EnumRobotStatus.INACTIVE: 'INACTIVE',
  EnumRobotStatus.ACTIVE: 'ACTIVE',
  EnumRobotStatus.BLCKED: 'BLCKED',
};

CustomField _$CustomFieldFromJson(Map<String, dynamic> json) => CustomField(
      type: json['type'] as String,
      value: json['value'] as String,
      label: json['label'] as String,
      key: json['key'] as String,
    );

Map<String, dynamic> _$CustomFieldToJson(CustomField instance) =>
    <String, dynamic>{
      'type': instance.type,
      'value': instance.value,
      'label': instance.label,
      'key': instance.key,
    };

CustomFieldTimer _$CustomFieldTimerFromJson(Map<String, dynamic> json) =>
    CustomFieldTimer(
      show: json['show'] as bool,
      font: json['font'] as String,
      position: $enumDecode(_$TimerPositionEnumMap, json['position']),
    );

Map<String, dynamic> _$CustomFieldTimerToJson(CustomFieldTimer instance) =>
    <String, dynamic>{
      'show': instance.show,
      'font': instance.font,
      'position': _$TimerPositionEnumMap[instance.position]!,
    };

const _$TimerPositionEnumMap = {
  TimerPosition.CT: 'CT',
  TimerPosition.CB: 'CB',
  TimerPosition.LT: 'LT',
  TimerPosition.RT: 'RT',
  TimerPosition.LB: 'LB',
  TimerPosition.RB: 'RB',
  TimerPosition.LC: 'LC',
  TimerPosition.RC: 'RC',
  TimerPosition.CC: 'CC',
};

WatchTag _$WatchTagFromJson(Map<String, dynamic> json) => WatchTag(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      templates: (json['DashTemplate'] as List<dynamic>?)
              ?.map((e) => WatchTemplate.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );

Map<String, dynamic> _$WatchTagToJson(WatchTag instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'DashTemplate': instance.templates,
    };
