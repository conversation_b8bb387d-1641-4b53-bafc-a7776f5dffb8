import 'package:Toooony/theme/theme.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:Toooony/controllers/language_controller.dart';

class TonySettingsPage extends StatefulWidget {
  const TonySettingsPage({super.key});

  @override
  State<TonySettingsPage> createState() => _TonySettingsPageState();
}

class _TonySettingsPageState extends State<TonySettingsPage> {
  @override
  Widget build(BuildContext context) {
    final theme = TonyTheme.of(context);
    final languageController = Get.find<LanguageController>();

    return Scaffold(
      backgroundColor: theme.background,
      appBar: AppBar(
        backgroundColor: theme.background,
        leading: IconButton(
          onPressed: () {
            Get.back();
          },
          icon: const Icon(
            Icons.arrow_back_ios_new_rounded,
            color: Colors.white,
          ),
        ),
        title: Text(
          'settings'.tr,
          style: TextStyle(
            color: theme.onBackground,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 15),
                padding: const EdgeInsets.symmetric(horizontal: 15),
                decoration: BoxDecoration(
                  color: theme.cardBackground,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 15),
                      decoration: BoxDecoration(
                        border: Border(
                          bottom: BorderSide(
                            width: 1.5,
                            color: theme.background,
                          ),
                        ),
                      ),
                      child: Row(
                        children: [
                          Text(
                            'name'.tr,
                            style: TextStyle(
                              color: theme.onBackground,
                              fontSize: 14,
                            ),
                          ),
                          const Spacer(),
                          Text(
                            'Toooony',
                            style: TextStyle(
                              color: theme.secondary,
                              fontSize: 14,
                              fontFamily: 'D-DIN-PRO',
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 15),
                      decoration: BoxDecoration(
                        border: Border(
                          bottom: BorderSide(
                            width: 1.5,
                            color: theme.background,
                          ),
                        ),
                      ),
                      child: Row(
                        children: [
                          Text(
                            'language'.tr,
                            style: TextStyle(
                              color: theme.onBackground,
                              fontSize: 14,
                            ),
                          ),
                          const Spacer(),
                          GestureDetector(
                            onTap: () {
                              // 切换语言
                              if (languageController.languageCode == 'zh') {
                                languageController.changeLanguage('en', 'US');
                              } else {
                                languageController.changeLanguage('zh', 'CN');
                              }
                            },
                            child: Text(
                              languageController.languageCode == 'zh'
                                  ? '中文'
                                  : 'English',
                              style: TextStyle(
                                color: theme.secondary,
                                fontSize: 14,
                                fontFamily: 'D-DIN-PRO',
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          Image.asset(
                            'assets/Right.png',
                            width: 24,
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 15),
                      decoration: BoxDecoration(
                        border: Border(
                          bottom: BorderSide(
                            width: 1.5,
                            color: theme.background,
                          ),
                        ),
                      ),
                      child: Row(
                        children: [
                          Text(
                            'theme'.tr,
                            style: TextStyle(
                              color: theme.onBackground,
                              fontSize: 14,
                            ),
                          ),
                          const Spacer(),
                          Text(
                            'Dark',
                            style: TextStyle(
                              color: theme.secondary,
                              fontSize: 14,
                              fontFamily: 'D-DIN-PRO',
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Image.asset(
                            'assets/Right.png',
                            width: 24,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }
}
