import 'dart:io';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';

class ImagePickerHandler {
  final ImagePicker _picker = ImagePicker();
  final ImageCropper _cropper = ImageCropper();

  Future<File?> pickAndCropImage(ImageSource source,
      [bool needCrop = false]) async {
    // 从相册中选择图片
    final XFile? pickedFile = await _picker.pickImage(source: source);

    if (pickedFile != null) {
      // 将 XFile 转换为 File
      File imageFile = File(pickedFile.path);

      if (!needCrop) {
        return imageFile;
      }

      final fileType = imageFile.path.split('.').lastOrNull ?? 'jpeg';

      if (fileType != 'gif' && fileType != 'avif') {
        // 裁剪图片为正方形
        final croppedFile = await _cropper.cropImage(
          sourcePath: imageFile.path,
          aspectRatio: const CropAspectRatio(ratioX: 1, ratioY: 1), // 设置为正方形
          compressQuality: 100,
        );

        if (croppedFile == null) {
          return imageFile;
        }

        return File(croppedFile.path);
      }

      return imageFile;
    } else {
      return null;
    }
  }
}
