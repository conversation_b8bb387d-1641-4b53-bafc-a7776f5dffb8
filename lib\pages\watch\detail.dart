import 'dart:convert';
import 'dart:io';
import 'package:Toooony/api/index.dart';
import 'package:Toooony/components/button.dart';
import 'package:Toooony/components/dialogs/loading.dart';
import 'package:Toooony/components/dialogs/url.dart';
import 'package:Toooony/components/picker.dart';
import 'package:Toooony/components/setting/field_picker.dart';
import 'package:Toooony/components/watch/face.dart';
import 'package:Toooony/controllers/watch.dart';
import 'package:Toooony/model/watch.dart';
import 'package:Toooony/pages/home.dart';
import 'package:Toooony/theme/theme.dart';
import 'package:Toooony/utils/dialog.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class DetailView extends StatefulWidget {
  final WatchTemplate template;

  const DetailView({super.key, required this.template});

  @override
  createState() => _DetailState();
}

class _DetailState extends State<DetailView> {
  late WatchTemplate template;
  final api = Get.find<ApiService>();

  File? selectedFile;

  final watchController = Get.find<WatchController>();

  TimerPosition timerPosition = TimerPosition.CC;
  bool showTimer = false;

  @override
  void initState() {
    super.initState();

    template = widget.template;
    showTimer = template.customFields.any((field) => field.type == 'timer');
  }

  Widget otherTemplates(TonyTheme theme) {
    return SizedBox(
      height: 140,
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          Column(
            children: [
              Container(
                width: 108,
                height: 108,
                decoration: const ShapeDecoration(
                  shape: OvalBorder(
                    side: BorderSide(width: 2, color: Color(0xFFFDE69A)),
                  ),
                ),
                child: Center(
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(999),
                    child: Image.asset(
                      'assets/Intersect.png',
                      width: 100,
                      height: 100,
                    ),
                  ),
                ),
              ),
              const SizedBox(
                height: 8,
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: theme.priamry,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  '男孩',
                  style: TextStyle(
                    color: theme.background,
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              )
            ],
          ),
          const SizedBox(
            width: 15,
          ),
        ],
      ),
    );
  }

  Widget buildURLField(TonyTheme theme, CustomField field) {
    return Container(
      decoration: BoxDecoration(
        color: theme.cardBackground,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          GestureDetector(
            onTap: () async {
              final res = await showEditURLDialog(context) ?? '';

              if (res.isEmpty) {
                return;
              }

              field.value = res;
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 15,
                vertical: 15,
              ),
              color: Colors.transparent,
              child: Row(
                children: [
                  Text(
                    '上传URL/Shader',
                    style: TextStyle(
                      color: theme.onBackground,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  const Spacer(),
                  Icon(
                    Icons.arrow_forward_ios,
                    color: theme.secondary,
                    size: 20,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildImageField(TonyTheme theme, CustomField field) {
    return Container(
      decoration: BoxDecoration(
        color: theme.cardBackground,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          GestureDetector(
            onTap: () async {
              if (field.type == 'image') {
                await showImagePicker(
                  theme,
                  ImagePickerSheet(
                    needCrop: true,
                    onSelect: (file) {
                      // 更换背景
                      if (field.key == 'background') {
                        selectedFile = file;
                        setState(() {});
                      }

                      Get.closeAllBottomSheets();
                    },
                  ),
                );
                Get.closeAllBottomSheets();
              }
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 15,
                vertical: 15,
              ),
              color: Colors.transparent,
              child: Row(
                children: [
                  Text(
                    '选择照片',
                    style: TextStyle(
                      color: theme.onBackground,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  const Spacer(),
                  Icon(
                    Icons.arrow_forward_ios,
                    color: theme.secondary,
                    size: 20,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildTimerField(TonyTheme theme, CustomField field) {
    final config = CustomFieldTimer.fromJson(jsonDecode(field.value));

    return Container(
      margin: const EdgeInsets.only(top: 15),
      decoration: BoxDecoration(
        color: theme.cardBackground,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(
              // vertical: 12,
              horizontal: 15,
            ),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: theme.background,
                  width: 1.5,
                ),
              ),
            ),
            child: Row(
              children: [
                Text(
                  '是否显示',
                  style: TextStyle(
                    color: theme.onBackground,
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                const Spacer(),
                Switch(
                    value: config.show,
                    activeTrackColor: theme.priamry,
                    activeColor: Colors.black,
                    onChanged: (value) {
                      config.show = value;
                      field.value = jsonEncode(config.toJson());
                      showTimer = value;
                      setState(() {});
                    }),
              ],
            ),
          ),
          GestureDetector(
            onTap: () async {
              final res = await showFieldPicker([
                const FieldPickerItem(
                    label: 'PingFang SC', value: 'PingFang SC'),
              ], '字体选择', initialValue: config.font);

              if (res != null) {
                config.font = res.value;
                field.value = jsonEncode(config.toJson());
                setState(() {});
              }
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                vertical: 15,
                horizontal: 15,
              ),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: theme.background,
                    width: 1.5,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Text(
                    '字体选择',
                    style: TextStyle(
                      color: theme.onBackground,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    config.font,
                    style: TextStyle(
                      color: theme.onBackground,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    color: theme.secondary,
                    size: 20,
                  ),
                ],
              ),
            ),
          ),
          GestureDetector(
            onTap: () async {
              final res = await showFieldPicker([
                const FieldPickerItem(label: '中间', value: TimerPosition.CC),
                const FieldPickerItem(label: '中上', value: TimerPosition.CT),
                const FieldPickerItem(label: '左上', value: TimerPosition.LT),
                const FieldPickerItem(label: '右上', value: TimerPosition.RT),
                const FieldPickerItem(label: '左下', value: TimerPosition.LB),
                const FieldPickerItem(label: '右下', value: TimerPosition.RB),
                const FieldPickerItem(label: '左中', value: TimerPosition.LC),
                const FieldPickerItem(label: '右中', value: TimerPosition.RC),
              ], '显示位置', initialValue: config.position);

              if (res != null) {
                timerPosition = res.value;
                config.position = res.value;
                field.value = jsonEncode(config.toJson());
                setState(() {});
              }
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                vertical: 15,
                horizontal: 15,
              ),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: theme.background,
                    width: 1.5,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Text(
                    '显示位置',
                    style: TextStyle(
                      color: theme.onBackground,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    config.positionLabel,
                    style: TextStyle(
                      color: theme.onBackground,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    color: theme.secondary,
                    size: 20,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildOneField(TonyTheme theme, CustomField field) {
    var builder = buildImageField;

    switch (field.type) {
      case 'image':
        builder = buildImageField;
        break;
      case 'url':
        builder = buildURLField;
        break;
      case 'timer':
        builder = buildTimerField;
        break;
      default:
        builder = buildImageField;
        break;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(
          height: 15,
        ),
        Align(
          alignment: Alignment.centerLeft,
          child: Text(
            field.label,
            style: TextStyle(
              color: theme.onBackground,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        const SizedBox(
          height: 10,
        ),
        builder(theme, field),
      ],
    );
  }

  Widget buildCustomFields(TonyTheme theme) {
    final customFields = template.customFields;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: customFields.map((field) {
        return buildOneField(theme, field);
      }).toList(),
    );
  }

  Widget tonyFace() {
    return Align(
      alignment: Alignment.center,
      child: TonyFace(
        width: 240,
        position: showTimer ? timerPosition : null,
        image: selectedFile != null ? selectedFile!.path : template.cover,
      ),
    );
  }

  Widget buildDeck() {
    return Container(
      width: Get.width,
      height: Get.width * 329 / 375,
      decoration: const BoxDecoration(
        color: Colors.transparent,
        image: DecorationImage(
          image: AssetImage('assets/home_bg.png'),
          fit: BoxFit.contain,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          // Tony的脸
          const SizedBox(
            height: 15,
          ),
          const Spacer(),
          tonyFace(),
          const SizedBox(
            height: 0,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = TonyTheme.of(context);
    return Scaffold(
      backgroundColor: theme.background,
      body: SafeArea(
        child: Stack(
          children: [
            SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  buildDeck(),
                  // const SizedBox(
                  //   height: 24,
                  // ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 15),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Align(
                        //   alignment: Alignment.centerLeft,
                        //   child: Text(
                        //     '说明',
                        //     style: TextStyle(
                        //       color: theme.onBackground,
                        //       fontSize: 16,
                        //       fontWeight: FontWeight.w500,
                        //     ),
                        //   ),
                        // ),
                        // const SizedBox(
                        //   height: 10,
                        // ),
                        // Align(
                        //   alignment: Alignment.centerLeft,
                        //   child: Text(
                        //     template.description,
                        //     textAlign: TextAlign.start,
                        //     style: TextStyle(
                        //       color: theme.secondary,
                        //       fontSize: 14,
                        //       fontWeight: FontWeight.w400,
                        //     ),
                        //   ),
                        // ),
                        const SizedBox(
                          height: 20,
                        ),
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            '自定义功能',
                            style: TextStyle(
                              color: theme.onBackground,
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        const SizedBox(
                          height: 10,
                        ),

                        // 之后放同类型表盘
                        // otherTemplates(theme),

                        buildCustomFields(theme),
                      ],
                    ),
                  ),

                  // Align(
                  //   alignment: Alignment.centerLeft,
                  //   child: Text(
                  //     '内容',
                  //     style: TextStyle(
                  //       color: theme.onBackground,
                  //       fontSize: 14,
                  //       fontWeight: FontWeight.w500,
                  //     ),
                  //   ),
                  // ),
                  // const SizedBox(
                  //   height: 10,
                  // ),
                  // Container(
                  //   decoration: BoxDecoration(
                  //     color: theme.cardBackground,
                  //     borderRadius: BorderRadius.circular(16),
                  //   ),
                  //   child: Column(
                  //     children: [
                  //       Container(
                  //         padding: const EdgeInsets.symmetric(
                  //           horizontal: 15,
                  //           vertical: 15,
                  //         ),
                  //         child: Row(
                  //           children: [
                  //             Text(
                  //               '选择照片',
                  //               style: TextStyle(
                  //                 color: theme.onBackground,
                  //                 fontSize: 14,
                  //                 fontWeight: FontWeight.w400,
                  //               ),
                  //             ),
                  //             const Spacer(),
                  //             const Text(
                  //               '时间',
                  //               style: TextStyle(
                  //                 color: Color(0xFFF3F3F3),
                  //                 fontSize: 14,
                  //                 fontFamily: 'PingFang SC',
                  //                 fontWeight: FontWeight.w400,
                  //                 height: 0,
                  //               ),
                  //             ),
                  //             Icon(
                  //               Icons.arrow_forward_ios,
                  //               color: theme.secondary,
                  //               size: 20,
                  //             ),
                  //           ],
                  //         ),
                  //       ),
                  //       Container(
                  //         padding: const EdgeInsets.symmetric(
                  //           horizontal: 15,
                  //           vertical: 15,
                  //         ),
                  //         child: Row(
                  //           children: [
                  //             Text(
                  //               '下部内容',
                  //               style: TextStyle(
                  //                 color: theme.onBackground,
                  //                 fontSize: 14,
                  //                 fontWeight: FontWeight.w400,
                  //               ),
                  //             ),
                  //             const Spacer(),
                  //             const Text(
                  //               '日期',
                  //               style: TextStyle(
                  //                 color: Color(0xFFF3F3F3),
                  //                 fontSize: 14,
                  //                 fontFamily: 'PingFang SC',
                  //                 fontWeight: FontWeight.w400,
                  //                 height: 0,
                  //               ),
                  //             ),
                  //             Icon(
                  //               Icons.arrow_forward_ios,
                  //               color: theme.secondary,
                  //               size: 20,
                  //             ),
                  //           ],
                  //         ),
                  //       ),
                  //     ],
                  //   ),
                  // ),
                  const SizedBox(
                    height: 400,
                  ),
                ],
              ),
            ),
            Positioned(
              top: 20 + MediaQuery.of(context).padding.top,
              left: 5,
              child: IconButton(
                onPressed: () {
                  Get.back();
                },
                icon: Icon(
                  Icons.arrow_back_ios,
                  color: theme.reversedPrimary,
                ),
              ),
            ),
            Positioned(
              bottom: 0,
              child: Container(
                width: Get.width,
                color: theme.background,
                height: 100,
                padding: const EdgeInsets.symmetric(
                  vertical: 15,
                  horizontal: 15,
                ),
                child: Center(
                  child: TonyButton(
                    onPressed: () async {
                      showTonyDialog(context, const LoadingDialog());

                      var cover = template.cover;

                      final fields = <CustomField>[];

                      try {
                        for (final field in template.customFields) {
                          if (field.key == 'background' &&
                              selectedFile != null) {
                            final tokenRes =
                                await api.file.getInstanceUploadToken();
                            final uploadRes = await api.file
                                .uploadFile(selectedFile!, tokenRes);
                            cover =
                                'https://toooony-1324559352.cos.ap-guangzhou.myqcloud.com/${uploadRes.key}';
                            fields.add(CustomField(
                                type: field.type,
                                value: cover,
                                label: field.label,
                                key: field.key));
                          } else {
                            fields.add(CustomField(
                                type: field.type,
                                value: field.value,
                                label: field.label,
                                key: field.key));
                          }
                        }

                        debugPrint('New cover: $cover');

                        final createdInstance =
                            await api.watch.createInstanceFromTemplate(
                          templateID: template.id,
                          customFields: fields,
                          cover: cover,
                        );

                        watchController.addInstance(createdInstance);

                        // 应用新添加的表盘
                        await watchController
                            .switchDashboard(createdInstance.id);

                        // 设置对应的Tab索引
                        final dashTabs = [
                          DashType.Official,
                          DashType.AI,
                          DashType.ImageBackground,
                          DashType.Dash3D,
                          DashType.URL,
                        ];
                        final targetIndex =
                            dashTabs.indexOf(createdInstance.type);
                        if (targetIndex != -1) {
                          watchController.currentTabIndex.value = targetIndex;
                        }

                        Get.closeAllDialogs();

                        // 跳转到首页
                        Get.back();
                        
                      } catch (e, stackTrace) {
                        e.printError();
                        stackTrace.printError();
                        Get.closeAllDialogs();
                      }
                    },
                    text: '添加到Toooony',
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
