import 'dart:io';

import 'package:Toooony/theme/theme.dart';
import 'package:Toooony/utils/image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

class ImagePickerSheet extends StatelessWidget {
  final void Function(File) onSelect;
  final _picker = ImagePicker();
  final bool multiSelect;
  final bool needCrop;
  final void Function(List<File>)? onMultiSelect;

  final handler = ImagePickerHandler();

  ImagePickerSheet({
    super.key,
    this.needCrop = false,
    required this.onSelect,
    this.multiSelect = false,
    this.onMultiSelect,
  });

  @override
  Widget build(BuildContext context) {
    final theme = TonyTheme.of(context);

    return Container(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          ListTile(
            title: Center(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.camera_alt,
                    color: theme.priamry,
                  ),
                  const SizedBox(width: 10),
                  Text(
                    'Camera',
                    style: TextStyle(
                      color: theme.onBackground,
                    ),
                  ),
                ],
              ),
            ),
            onTap: () async {
              final image =
                  await handler.pickAndCropImage(ImageSource.camera, needCrop);
              if (image != null) {
                onSelect(
                  File(
                    image.path,
                  ),
                );
              }
            },
            // onTap: () => onSelect('Male'),
          ),
          ListTile(
            // leading: const Icon(Icons.male),
            title: Center(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.camera,
                    color: theme.priamry,
                  ),
                  const SizedBox(width: 10),
                  Text(
                    'Gallery',
                    style: TextStyle(
                      color: theme.onBackground,
                    ),
                  ),
                ],
              ),
            ),
            onTap: () async {
              if (multiSelect) {
                final images = await _picker.pickMultiImage();
                if (images.isNotEmpty) {
                  onMultiSelect!(images.map((e) => File(e.path)).toList());
                }
                return;
              }

              final image =
                  await handler.pickAndCropImage(ImageSource.gallery, needCrop);
              if (image != null) {
                onSelect(File(image.path));
              }
            },
            // onTap: () => onSelect('Female'),
          ),
        ],
      ),
    );
  }
}

Future<T?> showImagePicker<T extends String>(
    TonyTheme theme, ImagePickerSheet sheet) {
  return Get.bottomSheet<T?>(
    barrierColor: Colors.transparent,
    backgroundColor: theme.modalBackground,
    sheet,
  );
}
