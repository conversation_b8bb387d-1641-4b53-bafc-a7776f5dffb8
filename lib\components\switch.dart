import 'package:Toooony/theme/theme.dart';
import 'package:flutter/cupertino.dart';

class TonySwitch extends StatelessWidget {
  final bool value;
  final Function(bool)? onChanged;

  const TonySwitch({
    super.key,
    this.value = true,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = TonyTheme.of(context);
    return CupertinoSwitch(
        value: value,
        activeTrackColor: theme.priamry,
        thumbColor: theme.background,
        inactiveTrackColor: theme.secondary,
        onChanged: onChanged);
  }
}
