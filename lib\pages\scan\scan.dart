import 'dart:convert';

import 'package:Toooony/api/index.dart';
import 'package:Toooony/components/dialogs/loading.dart';
import 'package:Toooony/controllers/bluetooth.dart';
import 'package:Toooony/controllers/watch.dart';
import 'package:Toooony/utils/dialog.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:qr_code_scanner_plus/qr_code_scanner_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:Toooony/theme/theme.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart' as flutter_blue_plus;

class BleInfo {
  final String deviceName;
  final String serviceUUID;

  BleInfo({required this.deviceName, required this.serviceUUID});

  factory BleInfo.fromJson(Map<String, dynamic> json) {
    return BleInfo(
      deviceName: json['deviceName'] as String,
      serviceUUID: json['serviceUUID'] as String,
    );
  }
}

class ScanResult {
  final String encStr;
  final String sn;
  final int time;
  final BleInfo ble;

  ScanResult(
      {required this.encStr,
      required this.sn,
      required this.time,
      required this.ble});

  factory ScanResult.fromJson(Map<String, dynamic> json) {
    return ScanResult(
      encStr: json['encStr'] as String,
      sn: json['sn'] as String,
      time: json['time'] as int,
      ble: BleInfo.fromJson(json['ble'] as Map<String, dynamic>),
    );
  }
}

class ScanQRPage extends StatefulWidget {
  const ScanQRPage({super.key});

  @override
  State<ScanQRPage> createState() => _ScanQRPageState();
}

class _ScanQRPageState extends State<ScanQRPage> {
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');

  QRViewController? controller;

  bool parsing = false;

  BLEController bleController = Get.find<BLEController>();

  @override
  void initState() {
    super.initState();
    Get.log('[扫码页面] 页面初始化开始');
    _requestCameraPermission();
  }

  Future<void> _requestCameraPermission() async {
    Get.log('[扫码页面] 请求摄像头权限');
    final status = await Permission.camera.request();
    Get.log('[扫码页面] 摄像头权限状态: $status');
    if (status.isDenied) {
      Get.log('[扫码页面] 摄像头权限被拒绝，返回上一页');
      Get.back();
    } else {
      Get.log('[扫码页面] 摄像头权限已获取');
    }
  }

  @override
  void dispose() {
    Get.log('[扫码页面] 页面销毁，断开蓝牙连接');
    bleController.disconnectDevice();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = TonyTheme.of(context);

    return Scaffold(
      backgroundColor: theme.background,
      appBar: CupertinoNavigationBar(
        backgroundColor: theme.background,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back_ios,
            color: Colors.white,
          ),
          onPressed: () => Get.back(),
        ),
        middle: Text(
          '扫描二维码',
          style: TextStyle(
            color: theme.onBackground,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      body: Stack(
        children: [
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTapDown: _onTapFocus,
            child: Container(
              decoration: BoxDecoration(
                // 添加轻微的遮罩来减少过曝
                color: Colors.black.withOpacity(0.05),
              ),
              child: QRView(
                key: qrKey,
                onQRViewCreated: _onQRViewCreated,
                overlay: QrScannerOverlayShape(
                  borderColor: theme.priamry,
                  borderRadius: 16,
                  borderLength: 30,
                  borderWidth: 4,
                  cutOutSize: 280, // 稍微增大扫描区域以适应屏幕距离
                ),
                // 针对屏幕扫描的摄像机配置
                cameraFacing: CameraFacing.back,
                formatsAllowed: const [BarcodeFormat.qrcode],
                // 优化扫描性能
                onPermissionSet: (ctrl, p) =>
                    _onPermissionSet(context, ctrl, p),
              ),
            ),
          ),
          // 屏幕扫描提示
          Positioned(
            bottom: 100,
            left: 20,
            right: 20,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.7),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Text(
                '📱 扫描屏幕提示：\n• 保持手机距离屏幕10-20cm\n• 避免屏幕反光，可轻微调整角度\n• 点击屏幕可手动对焦',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  height: 1.4,
                ),
                textAlign: TextAlign.left,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _onTapFocus(TapDownDetails details) {
    Get.log('[摄像机] 用户点击屏幕触发手动对焦');
    // 触发手动对焦 - 使用暂停和恢复来刷新对焦
    if (controller != null) {
      Get.log('[摄像机] 暂停摄像机以触发重新对焦');
      controller!.pauseCamera();
      Future.delayed(const Duration(milliseconds: 100), () {
        Get.log('[摄像机] 恢复摄像机');
        controller?.resumeCamera();
      });
    } else {
      Get.log('[摄像机] controller为空，无法执行对焦操作');
    }
  }

  void _onPermissionSet(BuildContext context, QRViewController ctrl, bool p) {
    Get.log('[摄像机] 权限设置回调: $p');
    if (!p) {
      Get.log('[摄像机] 权限未授予，显示提示');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('摄像机权限未授予')),
      );
    } else {
      Get.log('[摄像机] 权限已授予');
    }
  }

  Future<void> _setupCamera() async {
    Get.log('[摄像机] 开始设置摄像机');
    if (controller != null) {
      try {
        Get.log('[摄像机] 等待摄像机初始化完成');
        // 等待摄像机初始化完成
        await Future.delayed(const Duration(milliseconds: 500));

        // 为屏幕扫描优化摄像机设置
        try {
          Get.log('[摄像机] 优化摄像机设置以适合扫描屏幕内容');

          // 关闭闪光灯，避免反光影响屏幕扫描
          Get.log('[摄像机] 确保闪光灯关闭');
          await controller!.toggleFlash();
          await Future.delayed(const Duration(milliseconds: 100));
          await controller!.toggleFlash(); // 确保闪光灯是关闭状态

          Get.log('[摄像机] 闪光灯已关闭，减少屏幕反光');

          // 设置为连续对焦模式，提高扫描稳定性
          Get.log('[摄像机] 设置连续对焦模式');
        } catch (e) {
          Get.log('[摄像机] 优化摄像机设置时出错: $e');
        }

        Get.log('[摄像机] 摄像机初始化完成，已优化用于扫描屏幕内容');
        Get.log('[摄像机] 提示：扫描屏幕时请保持适当距离，避免反光');
      } catch (e) {
        Get.log('[摄像机] 设置摄像机参数失败: $e');
      }
    } else {
      Get.log('[摄像机] controller为空，无法设置摄像机');
    }
  }

  void _onQRViewCreated(QRViewController controller) {
    this.controller = controller;
    Get.log('[扫码] QRViewController 已创建');

    // 启用自动对焦和自动曝光
    _setupCamera();

    controller.scannedDataStream.listen((scanData) async {
      Get.log(
          '[扫码] 收到扫描数据: format=${scanData.format}, code=${scanData.code}, rawBytes=${scanData.rawBytes}');

      if (scanData.code != null) {
        Get.log('[扫码] 二维码内容不为空，开始处理: ${scanData.code}');

        if (parsing) {
          Get.log('[扫码] 正在处理中，忽略新的扫描结果');
          return;
        }

        if (scanData.code!.isEmpty) {
          Get.log('[扫码] 二维码内容为空，忽略');
          return;
        }

        parsing = true;
        Get.log('[扫码] 设置parsing=true，开始解析二维码');
        debugPrint('Barcode found! ${scanData.code}');

        try {
          Get.log('[扫码] 开始JSON解析二维码内容');
          final json = jsonDecode(scanData.code!);
          Get.log('[扫码] JSON解析成功: $json');

          Get.log('[扫码] 开始创建ScanResult对象');
          final scanResult = ScanResult.fromJson(json);
          Get.log(
              '[扫码] ScanResult创建成功: SN=${scanResult.sn}, Time=${scanResult.time}');

          Get.log('[扫码] 显示确认对话框');
          final confirmed = await showConfirmDialog(
            Get.context!,
            title: '绑定设备',
            content: '是否绑定设备 ${scanResult.sn} ?',
          );
          Get.log('[扫码] 用户确认结果: $confirmed');

          if (!confirmed) {
            Get.log('[扫码] 用户取消绑定，重置parsing状态');
            parsing = false;
            return;
          }

          Get.log('[扫码] 显示加载对话框');
          showTonyDialog(
            Get.context!,
            const LoadingDialog(),
          );

          Get.log(
              '[扫码] 设备信息 - SN: ${scanResult.sn} Time: ${scanResult.time} EncStr: ${scanResult.encStr}');
          Get.log(
              '[扫码] 蓝牙设备信息 - DeviceName: ${scanResult.ble.deviceName} ServiceUUID: ${scanResult.ble.serviceUUID}');

          Get.log('[扫码] 开始连接蓝牙设备');
          // 连接到二维码中指定的蓝牙设备
          await _connectToBleDevice(scanResult);
          Get.log('[扫码] 蓝牙设备连接流程已启动');
        } catch (e, stackTrace) {
          Get.log('[扫码] 解析或处理错误: $e');
          Get.log('[扫码] 错误堆栈: $stackTrace');
          Get.snackbar('错误', '无法解析二维码: $e');
          Get.closeOverlay();
        } finally {
          Get.log('[扫码] 重置parsing状态为false');
          parsing = false;
        }
      } else {
        Get.log('[扫码] 扫描结果为空，忽略');
      }
    });
  }

  Future<void> _connectToBleDevice(ScanResult scanResult) async {
    try {
      Get.log('[蓝牙连接] 开始连接蓝牙设备流程');
      Get.log(
          '[蓝牙连接] 目标设备: ${scanResult.ble.deviceName}, SN: ${scanResult.sn}');

      // 获取或创建 BLEController 实例
      BLEController bleController;
      if (Get.isRegistered<BLEController>()) {
        Get.log('[蓝牙连接] 使用现有的BLEController实例');
        bleController = Get.find<BLEController>();
      } else {
        Get.log('[蓝牙连接] 创建新的BLEController实例');
        bleController = Get.put(BLEController(deviceType: "CENTRAL"));
      }

      // 检查蓝牙权限
      Get.log('[蓝牙连接] 检查蓝牙权限');
      bool hasPermissions = await BLEController.checkAndRequestPermissions();
      Get.log('[蓝牙连接] 蓝牙权限检查结果: $hasPermissions');
      if (!hasPermissions) {
        Get.log('[蓝牙连接] 蓝牙权限不足，终止连接');
        Get.closeOverlay();
        Get.snackbar('错误', '蓝牙权限未完全授予，请检查权限设置');
        return;
      }

      // 检查蓝牙是否开启
      Get.log('[蓝牙连接] 检查蓝牙是否开启: ${bleController.isBleOn.value}');
      if (!bleController.isBleOn.value) {
        Get.log('[蓝牙连接] 蓝牙未开启，终止连接');
        Get.closeOverlay();
        Get.snackbar('错误', '请先开启蓝牙');
        return;
      }

      // 开始扫描指定设备
      Get.log('[蓝牙连接] 开始扫描设备: ${scanResult.ble.deviceName}');
      Get.log('[蓝牙连接] 使用序列号: ${scanResult.sn}');

      // 使用设备序列号进行扫描和连接
      Get.log('[蓝牙连接] 调用startScan方法');
      List<flutter_blue_plus.ScanResult> scanResults =
          await bleController.startScan(
        isFromActivate: true,
        bindedDevicesSerials: [scanResult.sn],
        onSuccess: () async {
          // 连接成功后的处理逻辑
          Get.log('[蓝牙连接] 设备连接成功，开始绑定流程');
          try {
            Get.log('[蓝牙连接] 等待2秒后开始API绑定');
            await Future.delayed(const Duration(seconds: 2));

            Get.log('[蓝牙连接] 获取API服务实例');
            final api = Get.find<ApiService>();

            Get.log('[蓝牙连接] 调用bindDevice API');
            Get.log(
                '[蓝牙连接] bindDevice参数 - SN: ${scanResult.sn}, EncStr: ${scanResult.encStr}, Time: ${scanResult.time}');
            final token = await api.auth.bindDevice(
              scanResult.sn,
              scanResult.encStr,
              scanResult.time,
            );
            Get.log('[蓝牙连接] 绑定设备成功，获得token: $token');

            Get.log('[蓝牙连接] 发送激活消息到设备');
            await bleController.sendMessage(MessageType.activate, token);
            Get.log('[蓝牙连接] 发送激活码成功');

            Get.log('[蓝牙连接] 关闭加载对话框');
            Get.closeOverlay();
            Get.snackbar('成功', '设备绑定并激活成功');

            Get.log('[蓝牙连接] 开始刷新设备列表和当前设备信息');
            final watchController = Get.find<WatchController>();

            try {
              // 使用专门的绑定后刷新方法
              Get.log('[蓝牙连接] 开始绑定后刷新流程');
              await watchController.refreshAfterBinding();
              Get.log('[蓝牙连接] 绑定后刷新完成');
            } catch (refreshError) {
              Get.log('[蓝牙连接] 刷新设备信息时出错: $refreshError');
              // 即使刷新失败，也不影响绑定成功的提示
            }

            Get.log('[蓝牙连接] 返回上一页面，传递绑定成功信号');
            Get.back(result: true);
          } catch (e, stackTrace) {
            Get.log('[蓝牙连接] 绑定设备失败: $e');
            Get.log('[蓝牙连接] 错误堆栈: $stackTrace');
            Get.closeOverlay();

            // 根据错误类型显示不同的提示信息
            String errorMessage = e.toString();
            if (errorMessage.contains('用户已绑定其他设备')) {
              Get.snackbar(
                '绑定失败',
                '您已绑定其他设备，每个用户只能绑定一个设备。请先在设备管理中解绑现有设备。',
                duration: const Duration(seconds: 5),
              );
            } else if (errorMessage.contains('设备已被其他用户绑定')) {
              Get.snackbar(
                '绑定失败',
                '该设备已被其他用户绑定，请检查设备序列号或联系客服处理。',
                duration: const Duration(seconds: 5),
              );
            } else {
              Get.snackbar(
                '错误',
                '设备绑定失败: $e',
                duration: const Duration(seconds: 4),
              );
            }
          }
        },
      );

      Get.log('[蓝牙连接] startScan完成，扫描结果数量: ${scanResults.length}');
      if (scanResults.isEmpty) {
        Get.log('[蓝牙连接] 未找到设备，显示失败消息');
        Get.closeOverlay();
        Get.snackbar('失败', '未找到指定设备，请确保设备在附近并已开启');
      } else {
        Get.log('[蓝牙连接] 找到 ${scanResults.length} 个设备，等待连接结果');
      }
      // 注意：成功的情况会在 onSuccess 回调中处理，包括关闭 overlay
    } catch (e, stackTrace) {
      Get.log('[蓝牙连接] 连接蓝牙设备异常: $e');
      Get.log('[蓝牙连接] 异常堆栈: $stackTrace');
      Get.closeOverlay();
      Get.snackbar('错误', '连接设备失败: $e');
    }
  }
}
// import 'dart:convert';

// import 'package:Toooony/api/index.dart';
// import 'package:Toooony/components/dialogs/loading.dart';
// import 'package:Toooony/controllers/bluetooth.dart';
// import 'package:Toooony/controllers/watch.dart';
// import 'package:Toooony/utils/dialog.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:mobile_scanner/mobile_scanner.dart';
// import 'package:permission_handler/permission_handler.dart';
// import 'package:Toooony/theme/theme.dart';
// import 'package:flutter_blue_plus/flutter_blue_plus.dart' as flutter_blue_plus;

// class BleInfo {
//   final String deviceName;
//   final String serviceUUID;

//   BleInfo({required this.deviceName, required this.serviceUUID});

//   factory BleInfo.fromJson(Map<String, dynamic> json) {
//     return BleInfo(
//       deviceName: json['deviceName'] as String,
//       serviceUUID: json['serviceUUID'] as String,
//     );
//   }
// }

// class ScanResult {
//   final String encStr;
//   final String sn;
//   final int time;
//   final BleInfo ble;

//   ScanResult(
//       {required this.encStr,
//       required this.sn,
//       required this.time,
//       required this.ble});

//   factory ScanResult.fromJson(Map<String, dynamic> json) {
//     return ScanResult(
//       encStr: json['encStr'] as String,
//       sn: json['sn'] as String,
//       time: json['time'] as int,
//       ble: BleInfo.fromJson(json['ble'] as Map<String, dynamic>),
//     );
//   }
// }

// class ScanQRPage extends StatefulWidget {
//   const ScanQRPage({super.key});

//   @override
//   State<ScanQRPage> createState() => _ScanQRPageState();
// }

// class _ScanQRPageState extends State<ScanQRPage> {
//   MobileScannerController controller = MobileScannerController(
//     detectionSpeed: DetectionSpeed.normal,
//     facing: CameraFacing.back,
//     formats: [BarcodeFormat.qrCode],
//     torchEnabled: false,
//   );

//   bool parsing = false;

//   BLEController bleController = Get.find<BLEController>();

//   @override
//   void initState() {
//     super.initState();
//     Get.log('[扫码页面] 页面初始化开始');
//     _requestCameraPermission();
//   }

//   Future<void> _requestCameraPermission() async {
//     Get.log('[扫码页面] 请求摄像头权限');
//     final status = await Permission.camera.request();
//     Get.log('[扫码页面] 摄像头权限状态: $status');
//     if (status.isDenied) {
//       Get.log('[扫码页面] 摄像头权限被拒绝，返回上一页');
//       Get.back();
//     } else {
//       Get.log('[扫码页面] 摄像头权限已获取');
//     }
//   }

//   @override
//   void dispose() {
//     Get.log('[扫码页面] 页面销毁，断开蓝牙连接');
//     bleController.disconnectDevice();
//     controller.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     final theme = TonyTheme.of(context);

//     return Scaffold(
//       backgroundColor: theme.background,
//       appBar: CupertinoNavigationBar(
//         backgroundColor: theme.background,
//         leading: IconButton(
//           icon: const Icon(
//             Icons.arrow_back_ios,
//             color: Colors.white,
//           ),
//           onPressed: () => Get.back(),
//         ),
//         middle: Text(
//           '扫描二维码',
//           style: TextStyle(
//             color: theme.onBackground,
//             fontSize: 16,
//             fontWeight: FontWeight.w500,
//           ),
//         ),
//       ),
//       body: Stack(
//         children: [
//           GestureDetector(
//             behavior: HitTestBehavior.opaque,
//             onTapDown: _onTapFocus,
//             child: Container(
//               decoration: BoxDecoration(
//                 color: Colors.black.withOpacity(0.05),
//               ),
//               child: MobileScanner(
//                 controller: controller,
//                 onDetect: _onDetect,
//               ),
//             ),
//           ),
//           // Custom overlay
//           Positioned.fill(
//             child: Stack(
//               children: [
//                 Positioned(
//                   top: 0,
//                   left: 0,
//                   right: 0,
//                   bottom: 0,
//                   child: Container(
//                     color: Colors.black.withOpacity(0.5),
//                   ),
//                 ),
//                 Center(
//                   child: Container(
//                     width: 280,
//                     height: 280,
//                     decoration: BoxDecoration(
//                       border: Border.all(
//                         color: theme.priamry,
//                         width: 4,
//                       ),
//                       borderRadius: BorderRadius.circular(16),
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//           ),
//           // 屏幕扫描提示
//           Positioned(
//             bottom: 100,
//             left: 20,
//             right: 20,
//             child: Container(
//               padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
//               decoration: BoxDecoration(
//                 color: Colors.black.withOpacity(0.7),
//                 borderRadius: BorderRadius.circular(8),
//               ),
//               child: const Text(
//                 '📱 扫描屏幕提示：\n• 保持手机距离屏幕10-20cm\n• 避免屏幕反光，可轻微调整角度\n• 点击屏幕可手动对焦',
//                 style: TextStyle(
//                   color: Colors.white,
//                   fontSize: 14,
//                   height: 1.4,
//                 ),
//                 textAlign: TextAlign.left,
//               ),
//             ),
//           ),
//           // Flashlight control button
//           Positioned(
//             bottom: 40,
//             right: 20,
//             child: IconButton(
//               icon: Icon(
//                 controller.torchEnabled ? Icons.flash_off : Icons.flash_on,
//                 color: Colors.white,
//                 size: 32,
//               ),
//               onPressed: () async {
//                 await controller.toggleTorch();
//                 setState(() {});
//               },
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   void _onTapFocus(TapDownDetails details) {
//     Get.log('[摄像机] 用户点击屏幕触发手动对焦');
//     Get.log('[摄像机] 停止摄像机以触发重新对焦');
//     controller.stop();
//     Future.delayed(const Duration(milliseconds: 100), () {
//       Get.log('[摄像机] 启动摄像机');
//       controller.start();
//     });
//   }

//   Future<void> _onDetect(BarcodeCapture capture) async {
//     final List<Barcode> barcodes = capture.barcodes;
//     for (final barcode in barcodes) {
//       Get.log(
//           '[扫码] 收到扫描数据: format=${barcode.format}, code=${barcode.rawValue}');

//       if (barcode.rawValue != null) {
//         Get.log('[扫码] 二维码内容不为空，开始处理: ${barcode.rawValue}');

//         if (parsing) {
//           Get.log('[扫码] 正在处理中，忽略新的扫描结果');
//           return;
//         }

//         if (barcode.rawValue!.isEmpty) {
//           Get.log('[扫码] 二维码内容为空，忽略');
//           return;
//         }

//         parsing = true;
//         Get.log('[扫码] 设置parsing=true，开始解析二维码');
//         debugPrint('Barcode found! ${barcode.rawValue}');

//         try {
//           Get.log('[扫码] 开始JSON解析二维码内容');
//           final json = jsonDecode(barcode.rawValue!);
//           Get.log('[扫码] JSON解析成功: $json');

//           Get.log('[扫码] 开始创建ScanResult对象');
//           final scanResult = ScanResult.fromJson(json);
//           Get.log(
//               '[扫码] ScanResult创建成功: SN=${scanResult.sn}, Time=${scanResult.time}');

//           Get.log('[扫码] 显示确认对话框');
//           final confirmed = await showConfirmDialog(
//             Get.context!,
//             title: '绑定设备',
//             content: '是否绑定设备 ${scanResult.sn} ?',
//           );
//           Get.log('[扫码] 用户确认结果: $confirmed');

//           if (!confirmed) {
//             Get.log('[扫码] 用户取消绑定，重置parsing状态');
//             parsing = false;
//             return;
//           }

//           Get.log('[扫码] 显示加载对话框');
//           showTonyDialog(
//             Get.context!,
//             const LoadingDialog(),
//           );

//           Get.log(
//               '[扫码] 设备信息 - SN: ${scanResult.sn} Time: ${scanResult.time} EncStr: ${scanResult.encStr}');
//           Get.log(
//               '[扫码] 蓝牙设备信息 - DeviceName: ${scanResult.ble.deviceName} ServiceUUID: ${scanResult.ble.serviceUUID}');

//           Get.log('[扫码] 开始连接蓝牙设备');
//           await _connectToBleDevice(scanResult);
//           Get.log('[扫码] 蓝牙设备连接流程已启动');
//         } catch (e, stackTrace) {
//           Get.log('[扫码] 解析或处理错误: $e');
//           Get.log('[扫码] 错误堆栈: $stackTrace');
//           Get.snackbar('错误', '无法解析二维码: $e');
//           Get.closeOverlay();
//         } finally {
//           Get.log('[扫码] 重置parsing状态为false');
//           parsing = false;
//         }
//       } else {
//         Get.log('[扫码] 扫描结果为空，忽略');
//       }
//     }
//   }

//   Future<void> _connectToBleDevice(ScanResult scanResult) async {
//     try {
//       Get.log('[蓝牙连接] 开始连接蓝牙设备流程');
//       Get.log(
//           '[蓝牙连接] 目标设备: ${scanResult.ble.deviceName}, SN: ${scanResult.sn}');

//       BLEController bleController;
//       if (Get.isRegistered<BLEController>()) {
//         Get.log('[蓝牙连接] 使用现有的BLEController实例');
//         bleController = Get.find<BLEController>();
//       } else {
//         Get.log('[蓝牙连接] 创建新的BLEController实例');
//         bleController = Get.put(BLEController(deviceType: "CENTRAL"));
//       }

//       Get.log('[蓝牙连接] 检查蓝牙权限');
//       bool hasPermissions = await BLEController.checkAndRequestPermissions();
//       Get.log('[蓝牙连接] 蓝牙权限检查结果: $hasPermissions');
//       if (!hasPermissions) {
//         Get.log('[蓝牙连接] 蓝牙权限不足，终止连接');
//         Get.closeOverlay();
//         Get.snackbar('错误', '蓝牙权限未完全授予，请检查权限设置');
//         return;
//       }

//       Get.log('[蓝牙连接] 检查蓝牙是否开启: ${bleController.isBleOn.value}');
//       if (!bleController.isBleOn.value) {
//         Get.log('[蓝牙连接] 蓝牙未开启，终止连接');
//         Get.closeOverlay();
//         Get.snackbar('错误', '请先开启蓝牙');
//         return;
//       }

//       Get.log('[蓝牙连接] 开始扫描设备: ${scanResult.ble.deviceName}');
//       Get.log('[蓝牙连接] 使用序列号: ${scanResult.sn}');

//       Get.log('[蓝牙连接] 调用startScan方法');
//       List<flutter_blue_plus.ScanResult> scanResults =
//           await bleController.startScan(
//         isFromActivate: true,
//         bindedDevicesSerials: [scanResult.sn],
//         onSuccess: () async {
//           Get.log('[蓝牙连接] 设备连接成功，开始绑定流程');
//           try {
//             Get.log('[蓝牙连接] 等待2秒后开始API绑定');
//             await Future.delayed(const Duration(seconds: 2));

//             Get.log('[蓝牙连接] 获取API服务实例');
//             final api = Get.find<ApiService>();

//             Get.log('[蓝牙连接] 调用bindDevice API');
//             Get.log(
//                 '[蓝牙连接] bindDevice参数 - SN: ${scanResult.sn}, EncStr: ${scanResult.encStr}, Time: ${scanResult.time}');
//             final token = await api.auth.bindDevice(
//               scanResult.sn,
//               scanResult.encStr,
//               scanResult.time,
//             );
//             Get.log('[蓝牙连接] 绑定设备成功，获得token: $token');

//             Get.log('[蓝牙连接] 发送激活消息到设备');
//             await bleController.sendMessage(MessageType.activate, token);
//             Get.log('[蓝牙连接] 发送激活码成功');

//             Get.log('[蓝牙连接] 关闭加载对话框');
//             Get.closeOverlay();
//             Get.snackbar('成功', '设备绑定并激活成功');

//             Get.log('[蓝牙连接] 开始刷新设备列表和当前设备信息');
//             final watchController = Get.find<WatchController>();

//             try {
//               Get.log('[蓝牙连接] 开始绑定后刷新流程');
//               await watchController.refreshAfterBinding();
//               Get.log('[蓝牙连接] 绑定后刷新完成');
//             } catch (refreshError) {
//               Get.log('[蓝牙连接] 刷新设备信息时出错: $refreshError');
//             }

//             Get.log('[蓝牙连接] 返回上一页面，传递绑定成功信号');
//             Get.back(result: true);
//           } catch (e, stackTrace) {
//             Get.log('[蓝牙连接] 绑定设备失败: $e');
//             Get.log('[蓝牙连接] 错误堆栈: $stackTrace');
//             Get.closeOverlay();

//             String errorMessage = e.toString();
//             if (errorMessage.contains('用户已绑定其他设备')) {
//               Get.snackbar(
//                 '绑定失败',
//                 '您已绑定其他设备，每个用户只能绑定一个设备。请先在设备管理中解绑现有设备。',
//                 duration: const Duration(seconds: 5),
//               );
//             } else if (errorMessage.contains('设备已被其他用户绑定')) {
//               Get.snackbar(
//                 '绑定失败',
//                 '该设备已被其他用户绑定，请检查设备序列号或联系客服处理。',
//                 duration: const Duration(seconds: 5),
//               );
//             } else {
//               Get.snackbar(
//                 '错误',
//                 '设备绑定失败: $e',
//                 duration: const Duration(seconds: 4),
//               );
//             }
//           }
//         },
//       );

//       Get.log('[蓝牙连接] startScan完成，扫描结果数量: ${scanResults.length}');
//       if (scanResults.isEmpty) {
//         Get.log('[蓝牙连接] 未找到设备，显示失败消息');
//         Get.closeOverlay();
//         Get.snackbar('失败', '未找到指定设备，请确保设备在附近并已开启');
//       } else {
//         Get.log('[蓝牙连接] 找到 ${scanResults.length} 个设备，等待连接结果');
//       }
//     } catch (e, stackTrace) {
//       Get.log('[蓝牙连接] 连接蓝牙设备异常: $e');
//       Get.log('[蓝牙连接] 异常堆栈: $stackTrace');
//       Get.closeOverlay();
//       Get.snackbar('错误', '连接设备失败: $e');
//     }
//   }
// }
