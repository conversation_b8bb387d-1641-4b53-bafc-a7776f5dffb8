{"buildFiles": ["C:\\dev\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\dev\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\company\\toooony\\tony-flutter\\android\\app\\.cxx\\Debug\\635g1r6l\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\dev\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\company\\toooony\\tony-flutter\\android\\app\\.cxx\\Debug\\635g1r6l\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}