import 'dart:ui';
import 'package:Toooony/components/dialogs/loading.dart';
import 'package:Toooony/components/watch/url_cover.dart';
import 'package:Toooony/controllers/watch.dart';
import 'package:Toooony/model/watch.dart';
import 'package:Toooony/pages/home.dart';
import 'package:Toooony/theme/theme.dart';
import 'package:Toooony/utils/dialog.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class EditWatchModal extends StatefulWidget {
  const EditWatchModal({super.key});

  @override
  State<StatefulWidget> createState() {
    return _EditWatchModalState();
  }
}

class _EditWatchModalState extends State<EditWatchModal>
    with SingleTickerProviderStateMixin {
  final watchController = Get.find<WatchController>();

  List<WatchInstance> instances = [];
  List<WatchInstance> deletedInstances = [];
  Map<DashType, List<WatchInstance>> instancesMap = {};
  WatchInstance? selectedInstance;

  late TabController tabController;

  @override
  void initState() {
    super.initState();

    // 首先初始化 TabController
    tabController = TabController(length: dashTabs.length, vsync: this);

    // 添加监听器来更新数量显示
    tabController.addListener(() {
      if (mounted) {
        setState(() {});
      }
    });

    // 然后初始化数据
    watchController.instancesMap.forEach((key, value) {
      Get.log('key: $key, value: ${value.length}');
      instancesMap[key] = List<WatchInstance>.from(value);
    });

    // 监听数据变化
    watchController.instancesMap.listen((_) {
      if (mounted) {
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    tabController.dispose();
    super.dispose();
  }

  Widget proxyDecorator(Widget child, int index, Animation<double> animation) {
    return AnimatedBuilder(
      animation: animation,
      builder: (BuildContext context, Widget? child) {
        final double animValue = Curves.easeInOut.transform(animation.value);
        final double elevation = lerpDouble(0, 6, animValue)!;
        return Material(
          elevation: elevation,
          color: Colors.transparent,
          shadowColor: Colors.black.withValues(alpha: 0.5),
          child: child,
        );
      },
      child: child,
    );
  }

  Widget watch(final BuildContext context, WatchInstance instance, int i) {
    final theme = TonyTheme.of(context);
    final isSelected = selectedInstance?.id == instance.id;
    return GestureDetector(
      key: ValueKey(instance.id),
      onTap: () {
        setState(() {
          selectedInstance = instance;
        });
      },
      child: Container(
        decoration: BoxDecoration(
            color: theme.cardBackground,
            borderRadius: BorderRadius.circular(8),
            border:
                isSelected ? Border.all(color: theme.priamry, width: 2) : null),
        padding: const EdgeInsets.all(15),
        margin: const EdgeInsets.only(top: 15),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(999),
              child: instance.type == DashType.URL
                  ? URLCover(
                      color: Colors.black,
                      instance: instance,
                      key: Key(instance.id.toString()))
                  : instance.cover.startsWith('asset')
                      ? Image.asset(
                          instance.cover,
                          width: 100,
                          height: 100,
                          fit: BoxFit.cover,
                        )
                      : Image.network(
                          instance.cover,
                          width: 100,
                          height: 100,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) =>
                              Container(
                            width: 100,
                            height: 100,
                            color: Colors.grey,
                            child: const Icon(Icons.error),
                          ),
                        ),
            ),
            const SizedBox(
              width: 10,
            ),
            Expanded(
              child: Text(
                instance.name,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
            GestureDetector(
              onTap: () async {
                debugPrint('Delete ${instance.id}');
                setState(() {
                  deletedInstances.add(instance);
                  instancesMap[instance.type]!
                      .removeWhere((item) => item.id == instance.id);
                });
                // final api = Get.find<ApiService>();

                // final confirmed =
                //     await showDeleteDialog(context, '确定要删除表盘吗？', '删除后无法恢复');

                // if (confirmed) {
                //   await api.watch.deleteInstance(instance.id);

                //   // Remove from both instances and instancesMap
                //   watchController.instances
                //       .removeWhere((item) => item.id == instance.id);

                //   for (final type in DashType.values) {
                //     watchController.instancesMap[type]!
                //         .removeWhere((item) => item.id == instance.id);
                //   }

                //   setState(() {});
                // }
              },
              child: Image.asset(
                'assets/icons/Reduce-one.png',
                width: 24,
                height: 24,
              ),
            ),
            const SizedBox(
              width: 20,
            ),
            ReorderableDragStartListener(
              index: i,
              key: ValueKey('drag-${instance.id}'), // Ensure unique key
              child: Image.asset(
                'assets/icons/drag.png',
                width: 24,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildTabViewContent(DashType type) {
    final instances = instancesMap[type] ?? [];

    return instances.isEmpty
        ? const SizedBox()
        : ReorderableListView.builder(
            itemCount: instances.length,
            itemBuilder: (context, index) {
              final instance = instances[index];
              return watch(context, instance, index);
            },
            onReorder: (oldIndex, newIndex) {
              setState(() {
                if (oldIndex < newIndex) {
                  newIndex -= 1;
                }
                final item = instancesMap[type]?.removeAt(oldIndex);
                if (item != null) {
                  instancesMap[type]?.insert(newIndex, item);
                }
              });
            },
            proxyDecorator: proxyDecorator,
          );
  }

  @override
  Widget build(BuildContext context) {
    final theme = TonyTheme.of(context);
    final currentType = dashTabs[tabController.index];
    final currentCount = (instancesMap[currentType] ?? []).length;

    return Container(
      width: Get.width,
      height: Get.height * 0.9,
      decoration: BoxDecoration(
        color: theme.modalBackground,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const SizedBox(height: 24),
          // 顶部取消和完成按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              GestureDetector(
                onTap: () {
                  if (deletedInstances.isNotEmpty) {
                    for (final instance in deletedInstances) {
                      watchController.instancesMap[instance.type]
                          ?.add(instance);
                    }
                    deletedInstances.clear();
                  }
                  Get.closeAllBottomSheets();
                },
                child: Text(
                  'cancel'.tr,
                  style: TextStyle(
                    color: theme.priamry,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              GestureDetector(
                onTap: () async {
                  showTonyDialog(context, const LoadingDialog());
                  if (deletedInstances.isNotEmpty) {
                    await watchController.removeInstances(deletedInstances);
                    deletedInstances.clear();
                  }

                  await watchController.updatePlaybook(instancesMap);

                  for (final type in DashType.values) {
                    if (instancesMap[type]?.isNotEmpty ?? false) {
                      final typeList = watchController.instancesMap[type];
                      if (typeList != null) {
                        typeList.value =
                            RxList<WatchInstance>.from(instancesMap[type]!);
                      }
                    }
                  }

                  Get.closeOverlay();
                  Get.closeAllBottomSheets();
                },
                child: Text(
                  'done'.tr,
                  style: TextStyle(
                    color: theme.priamry,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // TabBar 区域
          Row(
            children: [
              Expanded(
                child: TabBar(
                  tabs: dashTabs
                      .map((type) =>
                          Tab(text: dashTypesMap[type] ?? type.toString()))
                      .toList(),
                  controller: tabController,
                  indicatorSize: TabBarIndicatorSize.label,
                  indicator: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(width: 2, color: theme.priamry),
                    ),
                  ),
                  labelStyle: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                  unselectedLabelStyle: const TextStyle(
                    color: Color(0xFF747474),
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                  ),
                  tabAlignment: TabAlignment.start,
                  isScrollable: true,
                  indicatorPadding: const EdgeInsets.only(bottom: 4),
                  labelPadding: const EdgeInsets.symmetric(horizontal: 12),
                  dividerColor: Colors.transparent,
                ),
              ),
            ],
          ),

          // 数量显示 - 在TabBar下方
          Padding(
            padding: const EdgeInsets.all(4),
            child: Align(
              alignment: Alignment.centerRight,
              child: Text(
                '$currentCount/20',
                style: TextStyle(
                  fontSize: 14,
                  color: theme.secondary,
                ),
              ),
            ),
          ),

          Expanded(
            child: TabBarView(
              controller: tabController,
              children:
                  dashTabs.map((type) => buildTabViewContent(type)).toList(),
            ),
          ),
        ],
      ),
    );
  }
}
