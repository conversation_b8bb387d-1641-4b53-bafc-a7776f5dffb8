import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LanguageController extends GetxController {
  static const String LANGUAGE_CODE_KEY = 'languageCode';
  static const String COUNTRY_CODE_KEY = 'countryCode';

  final _languageCode = 'zh'.obs;
  final _countryCode = 'CN'.obs;

  String get languageCode => _languageCode.value;
  String get countryCode => _countryCode.value;
  Locale get locale => Locale(languageCode, countryCode);

  @override
  void onInit() {
    super.onInit();
    loadSavedLanguage();
  }

  Future<void> loadSavedLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    _languageCode.value = prefs.getString(LANGUAGE_CODE_KEY) ?? 'zh';
    _countryCode.value = prefs.getString(COUNTRY_CODE_KEY) ?? 'CN';
    updateLocale();
  }

  Future<void> changeLanguage(String langCode, String countryCode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(LANGUAGE_CODE_KEY, langCode);
    await prefs.setString(COUNTRY_CODE_KEY, countryCode);

    _languageCode.value = langCode;
    _countryCode.value = countryCode;
    updateLocale();
  }

  void updateLocale() {
    Get.updateLocale(locale);
  }
}
