import 'dart:io';
import 'dart:async';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;

import 'package:Toooony/api/index.dart';
import 'package:Toooony/model/file.dart';

class FileApi {
  final Api api;
  final String minioEndpoint;
  final String tencentOSSEndpoint;

  final connect = GetConnect();

  FileApi(this.api)
      : minioEndpoint =
            'https://static.luxhub.top:2097', // Replace with your MinIO endpoint
        tencentOSSEndpoint =
            'https://cos.ap-shanghai.myqcloud.com'; // Replace with your Tencent Cloud OSS endpoint

  Future<FileTokenResponse> getInstanceUploadToken() =>
      api.post('/file/instance/upload_token', {}).then(
        (response) => FileTokenResponse.fromJson(response.body),
      );

  String _getContentType(File file) {
    final extension = file.path.split('.').last.toLowerCase();
    switch (extension) {
      case 'jpg':
      case 'jpeg':
      case 'jpe':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'webp':
        return 'image/webp';
      default:
        return 'application/octet-stream';
    }
  }

  Future<PutResponse> uploadFile(File file, FileTokenResponse token) async {
    try {
      final fileBytes = file.readAsBytesSync();

      // 创建 PUT 请求
      final request = http.Request('PUT', Uri.parse(token.token));
      request.headers['Content-Type'] = _getContentType(file);
      request.headers['Accept'] = '*/*';
      request.headers['Accept-Encoding'] = 'gzip, deflate, br';
      request.headers['Connection'] = 'keep-alive';
      // request.headers['Content-Length'] = fileBytes.length.toString();
      request.bodyBytes = fileBytes;

      // 发送请求
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      print('Content-Type: ${_getContentType(file)}');
      print('Content-Length: ${fileBytes.length}');
      print('Response: ${response.statusCode} ${response.body}');
      print('Upload success: ${token.key}');
      return PutResponse(key: token.key);
    } catch (error) {
      print('Upload failed: $error');
      rethrow;
    }
  }
}

class PutResponse {
  final String key;
  PutResponse({required this.key});
}
