import 'package:json_annotation/json_annotation.dart';

part 'watch.g.dart';

@JsonSerializable()
class WebviewLoadingInfo {
  @JsonKey(defaultValue: false)
  final bool loading;

  WebviewLoadingInfo({required this.loading});

  factory WebviewLoadingInfo.fromJson(Map<String, dynamic> json) =>
      _$WebviewLoadingInfoFromJson(json);
}

// 对应后端DashType
enum WatchType {
  Official,
  ImageBackground,
  AI,
  Custom,
  URL,
}

@JsonSerializable()
class WatchTemplate {
  final int id;

  @Json<PERSON>ey(defaultValue: '')
  final String name;

  @JsonKey(defaultValue: '')
  final String version;

  @JsonKey(defaultValue: '')
  final String description;

  @Json<PERSON>ey(defaultValue: '')
  final String downloadSource;

  @JsonKey(defaultValue: '')
  final String cover;

  @JsonKey(defaultValue: [])
  final List<CustomField> customFields;

  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: WatchType.Official)
  final WatchType type;

  final DateTime createdAt;
  final DateTime updatedAt;

  WatchTemplate({
    required this.id,
    required this.type,
    required this.name,
    required this.version,
    required this.downloadSource,
    required this.description,
    required this.cover,
    required this.customFields,
    required this.createdAt,
    required this.updatedAt,
  });

  factory WatchTemplate.fromJson(Map<String, dynamic> json) =>
      _$WatchTemplateFromJson(json);
}

enum DashType {
  ImageBackground,
  AI,
  Custom,
  Dash3D,
  URL,
  Official,
}

@JsonSerializable()
class WatchInstance {
  final int id;
  final String name;
  final String description;
  final String cover;
  final int size;
  final DashType type;

  @JsonKey(defaultValue: [])
  final List<CustomField> customFields;

  final WatchTemplate template;

  final DateTime createdAt;
  final DateTime updatedAt;

  WatchInstance({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.cover,
    required this.size,
    required this.customFields,
    required this.template,
    required this.createdAt,
    required this.updatedAt,
  });

  factory WatchInstance.fromJson(Map<String, dynamic> json) =>
      _$WatchInstanceFromJson(json);
}

@JsonSerializable()
class Playinfo {
  List<int> dashboardIDs;

  Playinfo({
    required this.dashboardIDs,
  });

  factory Playinfo.fromJson(Map<String, dynamic> json) =>
      _$PlayinfoFromJson(json);
}

@JsonSerializable()
class Playbook {
  final int id;
  final int userID;
  final int deviceID;

  final Playinfo? playInfo;

  Playbook({
    required this.id,
    required this.userID,
    required this.deviceID,
    this.playInfo,
  });

  factory Playbook.fromJson(Map<String, dynamic> json) =>
      _$PlaybookFromJson(json);
}

enum EnumRobotStatus {
  INACTIVE, // 对接工厂用户，设备未激活
  ACTIVE,
  BLCKED,
} //

@JsonSerializable()
class Device {
  final int id;

  @JsonKey(defaultValue: '')
  final String serial;

  @JsonKey(defaultValue: '')
  final String encryptKey;

  @JsonKey(defaultValue: -1)
  final int currentDashboardID;

  @JsonKey(defaultValue: EnumRobotStatus.ACTIVE)
  final EnumRobotStatus status;

  final DateTime? lastOnline;
  final DateTime createdAt;
  final DateTime updatedAt;

  Device({
    required this.id,
    required this.serial,
    required this.encryptKey,
    required this.status,
    this.lastOnline,
    this.currentDashboardID = -1,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Device.fromJson(Map<String, dynamic> json) => _$DeviceFromJson(json);
}

class TemplateType {
  final int id;
  final String label;

  List<WatchTemplate> templates = [];

  TemplateType({
    required this.id,
    required this.label,
  });
}

@JsonSerializable()
class CustomField {
  String type;
  String value;
  String label;
  String key;

  CustomField(
      {required this.type,
      required this.value,
      required this.label,
      required this.key});

  factory CustomField.fromJson(Map<String, dynamic> json) =>
      _$CustomFieldFromJson(json);

  Map<String, dynamic> toJson() => _$CustomFieldToJson(this);
}

enum TimerPosition {
  CT,
  CB,
  LT,
  RT,
  LB,
  RB,
  LC,
  RC,
  CC,
}

@JsonSerializable()
class CustomFieldTimer {
  bool show;
  String font;
  TimerPosition position;

  String get positionLabel {
    switch (position) {
      case TimerPosition.CC:
        return '中间';
      case TimerPosition.CT:
        return '中上';
      case TimerPosition.CB:
        return '中下';
      case TimerPosition.LT:
        return '左上';
      case TimerPosition.RT:
        return '右上';
      case TimerPosition.LB:
        return '左下';
      case TimerPosition.RB:
        return '右下';
      case TimerPosition.LC:
        return '左中';
      case TimerPosition.RC:
        return '右中';
    }
  }

  CustomFieldTimer({
    required this.show,
    required this.font,
    required this.position,
  });

  factory CustomFieldTimer.fromJson(Map<String, dynamic> json) =>
      _$CustomFieldTimerFromJson(json);

  Map<String, dynamic> toJson() => _$CustomFieldTimerToJson(this);
}

@JsonSerializable()
class WatchTag {
  int id;

  String name;

  @JsonKey(name: 'DashTemplate', defaultValue: [])
  List<WatchTemplate> templates = [];

  WatchTag({
    required this.id,
    required this.name,
    this.templates = const [],
  });

  factory WatchTag.fromJson(Map<String, dynamic> json) =>
      _$WatchTagFromJson(json);
}
