import 'package:Toooony/api/index.dart';
import 'package:Toooony/controllers/auth.dart';
import 'package:Toooony/model/watch.dart';
import 'package:Toooony/pages/watch/detail.dart';
import 'package:Toooony/theme/theme.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

final List<String> images = [];

class WatchLibrary extends StatefulWidget {
  const WatchLibrary({super.key});

  @override
  State<StatefulWidget> createState() => _WatchLibraryState();
}

class _WatchLibraryState extends State<WatchLibrary> {
  List<WatchTag> tags = [];

  final refreshController = RefreshController(initialRefresh: true);

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final AuthController authController = Get.find<AuthController>();

      authController.token.addListener(() {
        if (mounted) {
          refreshController.requestRefresh();
        }
      });
    });
  }

  Widget row(
    BuildContext context,
    WatchTag tag,
  ) {
    final theme = TonyTheme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(
          height: 20,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15),
          child: Text(
            tag.name.tr,
            style: TextStyle(
              color: theme.reversedPrimary,
              fontSize: 16,
              fontFamily: 'PingFang SC',
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        const SizedBox(
          height: 10,
        ),
        if (tag.id == -1)
          SizedBox(
            width: Get.width,
            height: 104,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: images.length,
              itemBuilder: (ctx, i) => GestureDetector(
                onTap: () {},
                child: Container(
                  margin: const EdgeInsets.only(left: 15),
                  clipBehavior: Clip.antiAlias,
                  decoration: ShapeDecoration(
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(999))),
                  child: CachedNetworkImage(
                    imageUrl: images[i],
                    fit: BoxFit.cover,
                    width: 100,
                    height: 100,
                  ),
                ),
              ),
            ),
          )
        else
          SizedBox(
            width: Get.width,
            height: 104,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: tag.templates.length,
              itemBuilder: (ctx, i) => GestureDetector(
                onTap: () {
                  Get.to(
                      () => DetailView(
                            template: tag.templates[i],
                          ),
                      transition: Transition.rightToLeft);
                },
                child: Container(
                  margin: const EdgeInsets.only(left: 15),
                  clipBehavior: Clip.antiAlias,
                  decoration: ShapeDecoration(
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(999))),
                  child: CachedNetworkImage(
                    imageUrl: tag.templates[i].cover,
                    fit: BoxFit.cover,
                    width: 100,
                    height: 100,
                  ),
                ),
              ),
            ),
          )
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = TonyTheme.of(context);
    return Scaffold(
      backgroundColor: theme.background,
      body: SafeArea(
        child: SizedBox(
          height: Get.height,
          // padding: const EdgeInsets.symmetric(horizontal: 15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 20,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                child: Text(
                  'watch_library'.tr,
                  style: TextStyle(
                    color: theme.onBackground,
                    fontSize: 34,
                    fontFamily: 'PingFang SC',
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Expanded(
                child: SmartRefresher(
                  controller: refreshController,
                  onRefresh: () async {
                    try {
                      final api = Get.find<ApiService>();
                      final res = await api.watch.getWatchTags();
                      refreshController.refreshCompleted();
                      setState(() {
                        tags = res;
                      });
                    } catch (e) {
                      refreshController.refreshFailed();
                    }
                  },
                  child: ListView.builder(
                    itemCount: tags.length,
                    itemBuilder: (ctx, i) => row(context, tags[i]),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
