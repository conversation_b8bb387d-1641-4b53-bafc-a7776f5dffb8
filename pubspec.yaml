name: Toooony
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: ^3.5.1

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  ble_peripheral:
    git:
      url: https://git.luxhub.top:2097/tooony/ble_peripheral
  flutter_blue_plus: ^1.31.7
  cupertino_icons: ^1.0.8
  get: ^5.0.0-release-candidate-9.2
  flutter_svg: ^2.0.10+1
  animated_bottom_navigation_bar: ^1.3.3
  json_annotation: ^4.9.0
  shared_preferences: ^2.3.2
  fluttertoast: ^8.2.8
  numberpicker: ^2.1.2
  qr_code_scanner_plus: ^2.0.10+1
  socket_io_client: ^3.0.2
  pull_to_refresh_flutter3: ^2.0.2
  cached_network_image: ^3.4.1
  image_picker: ^1.1.2
  image_cropper: ^9.0.0
  qiniu_flutter_sdk: ^0.7.0
  flutter_launcher_icons: ^0.14.3
  permission_handler: ^11.4.0
  flutter_card_swiper: ^7.0.2
  card_swiper: ^3.0.1
  minio: ^3.5.7
  crypto: ^3.0.6
  http: ^1.4.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0
  json_serializable: ^6.7.1
  build_runner: ^2.4.8

flutter:
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
    - assets/Intersect.png
    - assets/icons/
    - launch_icons/

  fonts:
    - family: D-DIN-PRO
      fonts:
        - asset: fonts/D-DIN-PRO-400-Regular.otf
          weight: 400
        - asset: fonts/D-DIN-PRO-500-Medium.otf
          weight: 500
        - asset: fonts/D-DIN-PRO-600-SemiBold.otf
          weight: 600
        - asset: fonts/D-DIN-PRO-700-Bold.otf
          weight: 700

# flutter pub run build_runner build
flutter_launcher_icons:
  android: true
  ios: true
  image_path: "launch_icons/1024.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
