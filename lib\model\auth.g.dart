// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AuthToken _$AuthTokenFromJson(Map<String, dynamic> json) => AuthToken(
      token: json['token'] as String,
    );

Map<String, dynamic> _$AuthTokenToJson(AuthToken instance) => <String, dynamic>{
      'token': instance.token,
    };

AuthResponse _$AuthResponseFromJson(Map<String, dynamic> json) => AuthResponse(
      username: json['username'] as String? ?? '',
      token: json['token'] as String? ?? '',
      avatar: json['avatar'] as String? ?? '',
      roles:
          (json['roles'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      phoneNumber: json['phoneNumber'] as String? ?? '',
      wechatOpenId: json['wechatOpenId'] as String? ?? '',
      email: json['email'] as String? ?? '',
    );

Map<String, dynamic> _$AuthResponseToJson(AuthResponse instance) =>
    <String, dynamic>{
      'username': instance.username,
      'token': instance.token,
      'avatar': instance.avatar,
      'roles': instance.roles,
      'phoneNumber': instance.phoneNumber,
      'wechatOpenId': instance.wechatOpenId,
      'email': instance.email,
    };

User _$UserFromJson(Map<String, dynamic> json) => User(
      id: (json['id'] as num).toInt(),
      nickname: json['nickname'] as String? ?? '',
      avatar: json['avatar'] as String? ?? '',
      whatsup: json['whatsup'] as String? ?? '',
      backgroundUrl: json['backgroundUrl'] as String? ?? '',
      email: json['email'] as String? ?? '',
      gender:
          $enumDecodeNullable(_$GenderEnumMap, json['gender']) ?? Gender.OTHER,
      phoneNumber: json['phoneNumber'] as String? ?? '',
      wechatOpenID: json['wechatOpenID'] as String? ?? '',
      wechatUnionID: json['wechatUnionID'] as String? ?? '',
      wechatNickname: json['wechatNickname'] as String? ?? '',
      currentDeviceID: (json['currentDeviceID'] as num?)?.toInt(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      carBrand: json['carBrand'] as String? ?? '',
      carModel: json['carModel'] as String? ?? '',
      carColor: json['carColor'] as String? ?? '',
      carVerifyStatus: $enumDecodeNullable(
              _$CarVerifyStatusEnumMap, json['carVerifyStatus']) ??
          CarVerifyStatus.NONE,
    );

Map<String, dynamic> _$UserToJson(User instance) => <String, dynamic>{
      'id': instance.id,
      'nickname': instance.nickname,
      'avatar': instance.avatar,
      'whatsup': instance.whatsup,
      'email': instance.email,
      'phoneNumber': instance.phoneNumber,
      'wechatOpenID': instance.wechatOpenID,
      'wechatUnionID': instance.wechatUnionID,
      'wechatNickname': instance.wechatNickname,
      'backgroundUrl': instance.backgroundUrl,
      'carBrand': instance.carBrand,
      'carModel': instance.carModel,
      'carColor': instance.carColor,
      'gender': _$GenderEnumMap[instance.gender]!,
      'carVerifyStatus': _$CarVerifyStatusEnumMap[instance.carVerifyStatus]!,
      'currentDeviceID': instance.currentDeviceID,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$GenderEnumMap = {
  Gender.MALE: 'MALE',
  Gender.FEMALE: 'FEMALE',
  Gender.OTHER: 'OTHER',
};

const _$CarVerifyStatusEnumMap = {
  CarVerifyStatus.NONE: 'NONE',
  CarVerifyStatus.PENDING: 'PENDING',
  CarVerifyStatus.APPROVED: 'APPROVED',
  CarVerifyStatus.REJECTED: 'REJECTED',
};
