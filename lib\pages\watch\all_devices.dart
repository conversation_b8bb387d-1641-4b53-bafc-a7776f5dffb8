import 'package:Toooony/components/app_bar.dart';
import 'package:Toooony/components/dialogs/loading.dart';
import 'package:Toooony/controllers/watch.dart';
import 'package:Toooony/model/watch.dart';
import 'package:Toooony/theme/theme.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AllDevicesPage extends StatefulWidget {
  const AllDevicesPage({super.key});

  @override
  State<AllDevicesPage> createState() => _AllDevicesPageState();
}

class _AllDevicesPageState extends State<AllDevicesPage> {
  final WatchController watchController = Get.find<WatchController>();

  List<Device> devices = [];

  @override
  void initState() {
    super.initState();
    devices = watchController.devices.value;
  }

  Widget card(final BuildContext context, Device device) {
    final theme = TonyTheme.of(context);
    return Container(
      decoration: BoxDecoration(
        color: theme.cardBackground,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Align(
            alignment: Alignment.centerRight,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 5,
                vertical: 1,
              ),
              decoration: BoxDecoration(
                color: theme.reversedPrimary,
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(
                    8,
                  ),
                  bottomLeft: Radius.circular(
                    8,
                  ),
                ),
              ),
              child: const Text(
                '在线',
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w400,
                  color: Colors.black,
                ),
              ),
            ),
          ),
          const SizedBox(
            height: 10,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4.0),
            child: Text(
              device.serial,
              style: TextStyle(
                fontSize: 14,
                color: theme.onBackground,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(
            height: 10,
          ),
          Image.asset(
            'assets/Face.png',
            width: 80,
          ),
          const SizedBox(
            height: 20,
          ),
          GestureDetector(
            onTap: () async {
              showLoadingDialog();
              try {
                await watchController.switchToDevice(device);
              } catch (e) {
                Get.log('切换设备失败: $e');
              } finally {
                Get.closeAllDialogs();
              }
            },
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(999),
                border: Border.all(
                  color: theme.borderColor,
                ),
                color: Colors.transparent,
              ),
              padding: const EdgeInsets.symmetric(
                horizontal: 32,
                vertical: 5,
              ),
              child: Text(
                '切换',
                style: TextStyle(
                  color: theme.secondary,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = TonyTheme.of(context);
    return Scaffold(
      backgroundColor: theme.background,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 15,
          ),
          child: Column(
            children: [
              const TonyAppBar(
                middle: '全部设备',
              ),
              Expanded(
                child: GridView.count(
                  crossAxisCount: 2,
                  mainAxisSpacing: 15,
                  crossAxisSpacing: 15,
                  childAspectRatio: 0.65,
                  children: devices.map((d) => card(context, d)).toList(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
