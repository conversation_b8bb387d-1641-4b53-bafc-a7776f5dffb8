import 'dart:async';

import 'package:Toooony/api/index.dart';
import 'package:Toooony/components/button.dart';
import 'package:Toooony/components/dialogs/loading.dart';
import 'package:Toooony/controllers/auth.dart';
import 'package:Toooony/theme/theme.dart';
import 'package:Toooony/utils/dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class VerifyPage extends StatefulWidget {
  final String phone;

  const VerifyPage({super.key, required this.phone});

  @override
  State<StatefulWidget> createState() {
    return _VerifyPageState();
  }
}

class _VerifyPageState extends State<VerifyPage> {
  final codeController = TextEditingController(text: '');
  int countdown = 59;

  Timer? timer;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((t) {
      timer = Timer.periodic(const Duration(seconds: 1), (timer) {
        if (!mounted) {
          return;
        }
        if (countdown > 0 && countdown < 60) {
          setState(() {
            countdown = countdown - 1;
          });
        } else if (countdown == 0) {
          setState(() {
            countdown = 60;
          });
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: TonyTheme.of(context).background,
      appBar: AppBar(
        leading: GestureDetector(
          onTap: () => Get.back(),
          child: Icon(
            Icons.arrow_back_ios,
            color: TonyTheme.of(context).onBackground,
          ),
        ),
        backgroundColor: TonyTheme.of(context).background,
      ),
      body: SafeArea(
        child: Container(
          width: Get.width,
          padding: const EdgeInsets.symmetric(horizontal: 30),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 12,
              ),
              Text(
                'hello'.tr,
                style: TextStyle(
                  color: TonyTheme.of(context).onBackground,
                  fontSize: 24,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(
                height: 5,
              ),
              Text(
                'welcome_message'.tr,
                style: TextStyle(
                  color: TonyTheme.of(context).secondary,
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                ),
              ),
              const SizedBox(
                height: 48,
              ),
              Container(
                width: Get.width,
                // padding: const EdgeInsets.symmetric(vertical: 9),
                decoration: BoxDecoration(
                    border: Border(
                        bottom: BorderSide(
                            width: 0.24,
                            color: TonyTheme.of(context).borderColor))),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: TextField(
                        controller: codeController,
                        autofocus: true,
                        style: TextStyle(
                          color: TonyTheme.of(context).onBackground,
                          fontSize: 16,
                          fontFamily: 'PingFang SC',
                          fontWeight: FontWeight.w500,
                        ),
                        decoration: InputDecoration(
                          border: InputBorder.none,
                          filled: false,
                          hintText: 'verify_code_hint'.tr,
                        ),
                      ),
                    ),
                    SvgPicture.asset('assets/clear.svg'),
                  ],
                ),
              ),
              const SizedBox(
                height: 32,
              ),
              GestureDetector(
                onTap: () {},
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    GestureDetector(
                      onTap: () async {
                        if (countdown == 60) {
                          final api = Get.find<ApiService>();
                          await api.auth.requestSMSCode(widget.phone);
                          setState(() {
                            countdown = 59;
                          });
                        }
                      },
                      child: Text(
                        countdown < 60 ? '${countdown}s后可重新获取' : '重新获取',
                        style: TextStyle(
                          color: TonyTheme.of(context).secondary,
                          fontSize: 14,
                          fontFamily: 'PingFang SC',
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 28,
              ),
              TonyButton(
                onPressed: () async {
                  if (codeController.value.text.isEmpty) {
                    return;
                  }
                  showTonyDialog(context, const LoadingDialog());
                  // await Future.delayed(const Duration(seconds: 1));
                  try {
                    debugPrint(widget.phone);
                    debugPrint(codeController.value.text);
                    final api = Get.find<ApiService>();
                    final authController = Get.find<AuthController>();
                    final ticket = await api.auth.getTicketBySMS(
                        widget.phone, codeController.value.text);
                    final res = await api.auth.getJwtToken(ticket, false);
                    await authController.login(res.token);

                    Navigator.of(context).popUntil((pre) => pre.isFirst);
                    return;
                  } catch (e, st) {
                    // throw e;
                    e.printError();
                    st.printError();
                    Get.closeOverlay();
                  }
                },
                text: 'verify_and_login'.tr,
              ),
              const SizedBox(
                height: 28,
              ),
              GestureDetector(
                child: Text(
                  '密码登录',
                  style: TextStyle(
                    color: TonyTheme.of(context).secondary,
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
