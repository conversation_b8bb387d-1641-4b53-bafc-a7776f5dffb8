import 'package:Toooony/api/index.dart';
import 'package:Toooony/controllers/socketio.dart';
import 'package:Toooony/model/watch.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

final defaultInstancesMap = <DashType, RxList<WatchInstance>>{
  DashType.ImageBackground: <WatchInstance>[].obs,
  DashType.AI: <WatchInstance>[
    WatchInstance(
      id: -1,
      name: 'AI',
      description: 'AI',
      type: DashType.AI,
      cover: 'assets/watch1.png',
      size: 0,
      customFields: [],
      template: WatchTemplate(
        id: -1,
        name: 'AI',
        type: WatchType.AI,
        version: '',
        downloadSource: '',
        description: '',
        cover: '',
        customFields: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    )
  ].obs,
  DashType.Dash3D: <WatchInstance>[].obs,
  DashType.URL: <WatchInstance>[].obs,
  DashType.Official: <WatchInstance>[].obs,
};

final dashTypesMap = <DashType, String>{
  DashType.ImageBackground: '照片背景',
  DashType.AI: 'AI',
  DashType.Dash3D: '3D',
  DashType.URL: 'URL',
  DashType.Official: '官方',
};

class WatchController extends GetxController {
  final api = Get.find<ApiService>();
  final Rx<Playbook?> playbook = Rx(null);
  final instances = <WatchInstance>[].obs;
  final devices = <Device>[].obs;
  final Rx<Device?> device = Rx(null);
  final RxBool ledOn = false.obs;
  final RxBool hotspotOn = false.obs;
  final RxString hotspotName = 'Toooony'.obs;
  final RxString hotspotPassword = 'Toooony2025'.obs;

  final currentWatchID = (-1).obs;
  final currentTabIndex = 0.obs; // 添加当前Tab索引

  final instancesMap = defaultInstancesMap
      .map((key, value) => MapEntry(key, RxList<WatchInstance>.from(value)))
      .obs;

  @override
  void onInit() {
    super.onInit();
  }

  void resetInstancesMap() {
    instancesMap.value = defaultInstancesMap
        .map((key, value) => MapEntry(key, RxList<WatchInstance>.from(value)));
  }

  Future<void> unbindCurrentDevice() async {
    if (device.value == null) {
      Get.log('No current device');
      return;
    }

    await api.watch.unbindDevice(device.value!.id);
    await init();
  }

  void setLed(bool ledOn) {
    if (device.value == null) {
      Get.log('没有当前设备，无法设置LED灯');
      return;
    }

    final socket = Get.find<SocketManager>();
    socket.emit('control', {
      'deviceID': device.value!.id,
      'type': 'led',
      'lightsOn': ledOn,
    });

    this.ledOn.value = ledOn;
  }

  void setHotspot(bool hotspotOn) {
    if (device.value == null) {
      Get.log('没有当前设备，无法设置热点');
      return;
    }

    final socket = Get.find<SocketManager>();
    socket.emit('control', {
      'deviceID': device.value!.id,
      'type': 'hotspot',
      'hotspotOn': hotspotOn,
      'ssid': hotspotName.value,
      'password': hotspotPassword.value,
    });

    this.hotspotOn.value = hotspotOn;
  }

  WatchInstance? currentInstance() {
    // 先在 instances 列表中查找
    final found = instances.firstWhereOrNull((instance) => instance.id == currentWatchID.value);
    if (found != null) return found;

    // 如果没找到，遍历 instancesMap 的所有表盘
    for (final list in instancesMap.values) {
      final ai = list.firstWhereOrNull((instance) => instance.id == currentWatchID.value);
      if (ai != null) return ai;
    }

    // 兜底返回 null
    return null;
  }

  Future<void> init() async {
    final currentDevice = await api.watch.getCurrentDevice();

    debugPrint('Current device: ${currentDevice?.id}');

    device.value = currentDevice;

    if (currentDevice != null) {
      final playbookRes = await api.watch.getPlaybook(currentDevice.id);
      playbook.value = playbookRes;
    }

    await refreshInstances();

    if (currentDevice != null) {
      await refreshPlaybook();
    }

    await refreshAllDevices();

    if (currentDevice != null) {
      switchToDevice(currentDevice);
    }
  }

  Future<void> refreshAllDevices() async {
    final res = await api.watch.getDevices();

    devices.value = res;
  }

  /// 专门用于设备绑定后的刷新方法
  Future<void> refreshAfterBinding() async {
    Get.log('[WatchController] 开始绑定后刷新流程');

    try {
      // 1. 刷新所有设备列表
      Get.log('[WatchController] 步骤1: 刷新设备列表');
      await refreshAllDevices();

      // 2. 获取当前设备信息
      Get.log('[WatchController] 步骤2: 获取当前设备信息');
      final currentDevice = await api.watch.getCurrentDevice();
      device.value = currentDevice;

      // 3. 如果当前设备存在，刷新相关数据
      if (currentDevice != null) {
        Get.log('[WatchController] 步骤3: 刷新设备相关数据');
        final playbookRes = await api.watch.getPlaybook(currentDevice.id);
        playbook.value = playbookRes;

        await refreshInstances();
        await refreshPlaybook();

        // 切换到新绑定的设备
        await switchToDevice(currentDevice);
      }

      Get.log('[WatchController] 绑定后刷新完成');
      Get.log('[WatchController] 当前设备数量: ${devices.length}');
      Get.log('[WatchController] 当前设备ID: ${device.value?.id}');
    } catch (e) {
      Get.log('[WatchController] 绑定后刷新失败: $e');
      rethrow;
    }
  }

  Future<void> updatePlaybook(
      [Map<DashType, List<WatchInstance>>? _instancesMap]) async {
    if (device.value == null) {
      return;
    }

    final tags = (_instancesMap ?? instancesMap).keys;
    final map = _instancesMap ?? instancesMap;
    final orderedIDs = <int>[];

    for (final tag in tags) {
      for (final instance in map[tag]!) {
        orderedIDs.add(instance.id);
      }
    }

    final playbookRes = await api.watch.updatePlaybook(
        device.value!.id,
        Playbook(
            id: playbook.value!.id,
            userID: playbook.value!.userID,
            deviceID: playbook.value!.deviceID,
            playInfo: Playinfo(dashboardIDs: orderedIDs)));

    Get.log('Update playbook: ${playbookRes.playInfo?.dashboardIDs}');
    playbook.value = playbookRes;

    sortInstancesMap();
  }

  void sortInstancesMap() {
    resetInstancesMap();

    if (playbook.value?.playInfo != null) {
      Get.log('按照Playbook的id顺序更新instancesMap');
      for (var id in playbook.value!.playInfo!.dashboardIDs) {
        final instance =
            instances.firstWhereOrNull((instance) => instance.id == id);
        if (instance != null) {
          instancesMap[instance.type]!.add(instance);
        }
      }
    }
    Get.log(
        'Official instances: ${instancesMap[DashType.Official]!.map((e) => e.id).toList()}');
  }

  Future<void> refreshPlaybook() async {
    if (device.value == null) {
      return;
    }

    final playbookRes = await api.watch.getPlaybook(device.value!.id);
    playbook.value = playbookRes;

    final instancesIDs = instances.map<int>((e) => e.id).toList()..add(-1);

    Get.log(
        'Playbook: ${playbook.value?.playInfo?.dashboardIDs} \n Instances: $instancesIDs');

    final _playbookSet =
        Set.from(playbook.value?.playInfo?.dashboardIDs ?? <int>[]);
    final _instancesSet = Set.from(instancesIDs);

    final containsAll = _playbookSet.containsAll(_instancesSet);

    Get.log('containsAll: $containsAll');

    if (!containsAll) {
      playbook.value!.playInfo!.dashboardIDs = instancesIDs;
      Get.log('Playbook和instances不一致，更新云端playbook');
      playbook.value =
          await api.watch.updatePlaybook(device.value!.id, playbook.value!);
    } else {
      Get.log('Playbook和instances一致，不更新云端playbook');
    }

    sortInstancesMap();
  }

  Future<void> removeInstances(List<WatchInstance> instances) async {
    await api.watch.deleteInstances(instances.map((e) => e.id).toList());
    await refreshInstances();
  }

  Future<void> refreshInstances() async {
    final instances = await api.watch.getInstances();
    this.instances.value = instances;

    await refreshPlaybook();
    // instancesMap.value = defaultInstancesMap
    //     .map((key, value) => MapEntry(key, RxList<WatchInstance>.from(value)));

    // for (var instance in instances) {
    //   instancesMap[instance.type]!.add(instance);
    // }
  }

  Future<void> switchToDevice(Device device) async {
    final deviceInfo = await api.watch.getDeviceInfo(device.id);
    this.device.value = deviceInfo;

    final playbookRes = await api.watch.getPlaybook(device.id);
    playbook.value = playbookRes;

    currentWatchID.value = deviceInfo.currentDashboardID;

    final ins = currentInstance();

    if (ins == null) {
      await refreshInstances();
    }
  }

Future<void> switchDashboard(int instanceID) async {
    if (device.value == null) {
      debugPrint('No device selected');
      return;
    }

    // 先本地更新 UI，立即显示新表盘（乐观更新）
    currentWatchID.value = instanceID;

    final socket = Get.find<SocketManager>();

    // 通过 socket 通知设备（异步，不阻塞 UI）
    socket.emit('control', {
      'deviceID': device.value!.id,
      'type': 'switch',
      'targetID': instanceID,
    });

    // 可选：添加超时检查，如果 0.5秒后设备未响应，显示提示
    Future.delayed(const Duration(milliseconds: 500), () {
      if (currentWatchID.value == instanceID) {
        Get.snackbar('提示', '表盘切换中，请稍等...');
      }
    });
  }

  Future<void> switchBrightness(int brightness) async {
    final socket = Get.find<SocketManager>();

    socket.emit('control', {
      'deviceID': device.value!.id,
      'type': 'brightness',
      'brightness': brightness,
    });
  }

  void addInstance(WatchInstance instance) {
    instances.insert(0, instance);
    instancesMap[instance.type]!.insert(0, instance);

    updatePlaybook();
  }

  onLogout() {
    playbook.value = null;
    device.value = null;
    currentWatchID.value = -1;
    instances.clear();
    devices.clear();
  }
}
