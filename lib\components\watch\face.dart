import 'dart:io';

import 'package:Toooony/model/watch.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

final weekDays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];

class TonyFace extends StatelessWidget {
  final String image;
  final double width;

  final TimerPosition? position;

  final ratio = 0.7;

  const TonyFace(
      {super.key,
      this.width = 200,
      this.image = 'assets/watch1.png',
      this.position});

  Widget _buildTimer() {
    if (position == null) return const SizedBox();

    final time = DateFormat('HH:mm').format(DateTime.now());

    final weekday = weekDays[DateTime.now().weekday - 1];

    var crossAxisAlignment = CrossAxisAlignment.center;
    var mainAxisAlignment = MainAxisAlignment.center;

    if (position == TimerPosition.LT ||
        position == TimerPosition.LC ||
        position == TimerPosition.LB) {
      crossAxisAlignment = CrossAxisAlignment.start;
    } else if (position == TimerPosition.RT ||
        position == TimerPosition.RC ||
        position == TimerPosition.RB) {
      crossAxisAlignment = CrossAxisAlignment.end;
    }

    if (position == TimerPosition.CT ||
        position == TimerPosition.LT ||
        position == TimerPosition.RT) {
      mainAxisAlignment = MainAxisAlignment.start;
    } else if (position == TimerPosition.CB ||
        position == TimerPosition.LB ||
        position == TimerPosition.RB) {
      mainAxisAlignment = MainAxisAlignment.end;
    }

    bool isCenter = false;

    if (position == TimerPosition.CT ||
        position == TimerPosition.CB ||
        position == TimerPosition.CC) {
      isCenter = true;
    }

    return Column(
      crossAxisAlignment: crossAxisAlignment,
      mainAxisAlignment: mainAxisAlignment,
      children: [
        if (!isCenter)
          Text(
            '$weekday ${DateTime.now().day}',
            style: const TextStyle(
              fontSize: 8,
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        Text(
          time,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        if (isCenter)
          Text(
            '$weekday ${DateTime.now().day}',
            style: const TextStyle(
              fontSize: 8,
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
      ],
    );
  }

  Alignment _getAlignment() {
    switch (position) {
      case TimerPosition.CT:
        return Alignment.topCenter;
      case TimerPosition.LT:
        return Alignment.topLeft;
      case TimerPosition.RT:
        return Alignment.topRight;
      case TimerPosition.LB:
        return Alignment.bottomLeft;
      case TimerPosition.RB:
        return Alignment.bottomRight;
      case TimerPosition.LC:
        return Alignment.centerLeft;
      case TimerPosition.RC:
        return Alignment.centerRight;
      case TimerPosition.CC:
        return Alignment.center;
      case TimerPosition.CB:
        return Alignment.bottomCenter;
      default:
        return Alignment.center;
    }
  }

  @override
  Widget build(BuildContext context) {
    final w = width * ratio;

    return Stack(
      children: [
        Positioned(
          top: width * (1 - ratio - 0.17) / 2,
          left: width * (1 - ratio + 0.03) / 2,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(999),
            child: SizedBox(
              width: w,
              height: w,
              child: Stack(
                children: [
                  image.startsWith('http')
                      ? CachedNetworkImage(
                          imageUrl: image,
                          width: w,
                          height: w,
                          fit: BoxFit.cover,
                        )
                      : image.startsWith('asset')
                          ? Image.asset(
                              image,
                              width: w,
                              height: w,
                              fit: BoxFit.cover,
                            )
                          : Image.file(
                              File(image),
                              width: w,
                              height: w,
                              fit: BoxFit.cover,
                            ),
                  if (position != null)
                    Positioned.fill(
                      child: Align(
                        alignment: _getAlignment(),
                        child: Padding(
                          padding: EdgeInsets.all(w * 0.18),
                          child: _buildTimer(),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),       
        Image.asset(
          'assets/tony-default.png',
          fit: BoxFit.cover,
          width: width,
          height: width,
        ),
      ],
    );
  }
}
