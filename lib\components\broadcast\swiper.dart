import 'package:Toooony/components/broadcast/card.dart';
import 'package:Toooony/model/broadcast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_card_swiper/flutter_card_swiper.dart';
import 'package:get/get.dart';

const img =
    'https://m.bcoderss.com/wp-content/uploads/ios/iphone/%E5%8A%A8%E6%BC%AB/%E6%97%A5%E6%BC%AB_%E5%A4%A9%E6%B0%94%E4%B9%8B%E5%AD%90_%E5%A4%A9%E9%87%8E%E9%98%B3%E8%8F%9C_%E6%A3%AE%E5%B2%9B%E5%B8%86%E9%AB%98.jpg';

final List<BroadCastModel> _cards = [
  // BroadCastModel(
  //   id: 1,
  //   name: '<PERSON>on Musk',
  //   description: 'Everything is possible',
  //   image: 'assets/pc_1.jpg',
  // ),
  BroadCastModel(
    id: 4,
    name: 'Eva',
    description: 'Everything is possible',
    image: 'assets/pc_4.jpg',
  ),
  BroadCastModel(
    id: 2,
    name: '<PERSON> <PERSON>',
    description: 'Everything is possible',
    image: 'assets/pc_2.png',
  ),
  BroadCastModel(
    id: 3,
    name: 'Lisa Wu2',
    description: 'Everything is possible',
    image: 'assets/pc_3.png',
  ),
];

List<Widget> _cards2 = [
  BroadCaseCard(model: _cards[0]),
  BroadCaseCard(model: _cards[1]),
  BroadCaseCard(model: _cards[2]),
  // BroadCaseCard(model: _cards[3]),
];

class BroadcastSwiper extends StatefulWidget {
  final List<BroadCaseCard> cards;

  const BroadcastSwiper({super.key, this.cards = const []});

  @override
  State<BroadcastSwiper> createState() => _BroadcastSwiperState();
}

class _BroadcastSwiperState extends State<BroadcastSwiper> {
  // List<BroadCastModel> cards = [];

  List<Widget> cards = [];

  final cardSwiperController = CardSwiperController();

  @override
  void initState() {
    super.initState();
    cards = _cards2;
    // cards = _cards
    //     .map(
    //       (e) => Container(
    //         child: BroadCaseCard(model: e),
    //       ),
    //     )
    //     .toList();
    Get.log('onSwipe refresh');
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: Get.width,
      height: Get.width * 1.08,
      child: CardSwiper(
        // controller: cardSwiperController,
        padding: const EdgeInsets.symmetric(),
        initialIndex: 0,
        cardBuilder: (context, index, percentThresholdX, percentThresholdY) {
          return cards[index];
        },
        cardsCount: cards.length,
        numberOfCardsDisplayed: 3,
        isLoop: true,
        allowedSwipeDirection: const AllowedSwipeDirection.symmetric(
            horizontal: true, vertical: false),
        backCardOffset: const Offset(0, 35),
        threshold: 1,
      ),
    );
  }
}
