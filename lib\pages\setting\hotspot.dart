import 'package:Toooony/components/app_bar.dart';
import 'package:Toooony/theme/theme.dart';
import 'package:flutter/material.dart';
import 'package:Toooony/controllers/socketio.dart';
import 'package:Toooony/controllers/auth.dart';
import 'package:get/get.dart';

class HotspotSettingPage extends StatefulWidget {
  const HotspotSettingPage({super.key});

  @override
  State<HotspotSettingPage> createState() => _HotspotSettingPageState();
}

class _HotspotSettingPageState extends State<HotspotSettingPage> {
  bool isHotspotEnabled = false;
  final socketManager = Get.find<SocketManager>();
  final authController = Get.find<AuthController>();

  void _toggleHotspot(bool enabled) {
    setState(() {
      isHotspotEnabled = enabled;
    });

    // 检查用户是否已登录
    if (authController.token.isEmpty) {
      debugPrint('用户未登录，无法发送socket事件');
      Get.snackbar('错误', '请先登录');
      return;
    }

    // 检查socket连接状态
    if (socketManager.socket == null) {
      debugPrint('Socket未初始化，尝试初始化socket...');
      socketManager.initSocket();

      // 给socket一点时间连接
      Future.delayed(const Duration(seconds: 1), () {
        _sendHotspotEvent(enabled);
      });
    } else if (socketManager.socket!.connected) {
      debugPrint('Socket已连接，发送热点事件');
      _sendHotspotEvent(enabled);
    } else {
      debugPrint('Socket未连接，状态: ${socketManager.socket!.connected}');
      Get.snackbar('错误', 'Socket连接异常，请重试');
    }
  }

  void _sendHotspotEvent(bool enabled) {
    final event = enabled ? 'enable' : 'disable';
    debugPrint('发送热点事件: hotspot -> action: $event');

    socketManager.emit('hotspot', {'action': event});

    // 显示操作反馈
    Get.snackbar(
      '热点控制',
      enabled ? '正在开启热点...' : '正在关闭热点...',
      duration: const Duration(seconds: 2),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = TonyTheme.of(context);
    return Scaffold(
      backgroundColor: theme.background,
      body: SafeArea(
        child: Column(
          children: [
            const TonyAppBar(
              middle: '热点设置',
            ),
            const SizedBox(
              height: 20,
            ),
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 15),
              decoration: BoxDecoration(
                color: theme.cardBackground,
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 15),
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 15),
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          width: 1.5,
                          color: theme.background,
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        Text(
                          '名称',
                          style: TextStyle(
                            color: theme.onBackground,
                            fontSize: 14,
                          ),
                        ),
                        const Spacer(),
                        Text(
                          'Toooony998',
                          style: TextStyle(
                            color: theme.secondary,
                            fontSize: 14,
                            fontFamily: 'D-DIN-PRO',
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Image.asset(
                          'assets/Right.png',
                          width: 24,
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 15),
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          width: 1.5,
                          color: theme.background,
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        Text(
                          '密码',
                          style: TextStyle(
                            color: theme.onBackground,
                            fontSize: 14,
                          ),
                        ),
                        const Spacer(),
                        Text(
                          '123123123',
                          style: TextStyle(
                            color: theme.secondary,
                            fontSize: 14,
                            fontFamily: 'D-DIN-PRO',
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Image.asset(
                          'assets/Right.png',
                          width: 24,
                        ),
                      ],
                    ),
                  ),
                  // 热点控制section
                  Container(
                    decoration: BoxDecoration(
                      color: theme.cardBackground,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(vertical: 15),
                          decoration: BoxDecoration(
                            border: Border(
                              bottom: BorderSide(
                                width: 1.5,
                                color: theme.background,
                              ),
                            ),
                          ),
                          child: Row(
                            children: [
                              Text(
                                '热点控制',
                                style: TextStyle(
                                  color: theme.onBackground,
                                  fontSize: 14,
                                ),
                              ),
                              const Spacer(),
                              Switch(
                                value: isHotspotEnabled,
                                onChanged: _toggleHotspot,
                                activeColor: theme.priamry,
                              ),
                            ],
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(vertical: 10),
                          child: Row(
                            children: [
                              Text(
                                'Socket状态: ${socketManager.socket?.connected == true ? "已连接" : "未连接"}',
                                style: TextStyle(
                                  color: theme.secondary,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(vertical: 15),
                          child: Row(
                            children: [
                              Expanded(
                                child: ElevatedButton(
                                  onPressed: () => _toggleHotspot(true),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: theme.priamry,
                                    foregroundColor: Colors.white,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 12),
                                  ),
                                  child: const Text(
                                    '开启热点',
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 15),
                              Expanded(
                                child: ElevatedButton(
                                  onPressed: () => _toggleHotspot(false),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: theme.secondary,
                                    foregroundColor: Colors.white,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 12),
                                  ),
                                  child: const Text(
                                    '关闭热点',
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(
              height: 20,
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 15),
              alignment: Alignment.centerLeft,
              child: Text(
                '已连接设备',
                style: TextStyle(
                  color: theme.onBackground,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            // Container(
            //   margin: const EdgeInsets.symmetric(horizontal: 15),
            //   decoration: BoxDecoration(
            //     color: theme.cardBackground,
            //     borderRadius: BorderRadius.circular(8),
            //   ),
            //   padding: const EdgeInsets.symmetric(horizontal: 15),
            //   child: Column(
            //     children: [
            //       Container(
            //         padding: const EdgeInsets.symmetric(vertical: 15),
            //         decoration: BoxDecoration(
            //           border: Border(
            //             bottom: BorderSide(
            //               width: 1.5,
            //               color: theme.background,
            //             ),
            //           ),
            //         ),
            //         child: Row(
            //           children: [
            //             Text(
            //               'Tesla Model X',
            //               style: TextStyle(
            //                 color: theme.onBackground,
            //                 fontSize: 14,
            //               ),
            //             ),
            //             const Spacer(),
            //             const Text(
            //               '移除',
            //               style: TextStyle(
            //                 color: Color(0xffF95151),
            //                 fontSize: 14,
            //                 fontFamily: 'D-DIN-PRO',
            //                 fontWeight: FontWeight.w500,
            //               ),
            //             ),
            //           ],
            //         ),
            //       ),
            //     ],
            //   ),
            // ),
          ],
        ),
      ),
    );
  }
}
