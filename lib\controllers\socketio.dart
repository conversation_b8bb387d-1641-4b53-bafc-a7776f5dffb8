import 'package:Toooony/controllers/auth.dart';
import 'package:Toooony/utils/constants.dart';
import 'package:get/get.dart';
import 'package:socket_io_client/socket_io_client.dart';

class SocketManager extends GetxService {
  Socket? socket;

  @override
  void onInit() {
    super.onInit();
  }

  void initSocket() {
    final authController = Get.find<AuthController>();

    if (socket != null) {
      socket!.destroy();
      socket = null;
    }

    socket = io(SOCKET_URL, {
      'path': '/ws',
      'transports': ['websocket'],
      'autoConnect': false,
      'auth': {
        'token': 'Bearer ${authController.token.value}',
      }
    });

    print('初始化SocketIO连接...');

    socket!.onDisconnect((data) {
      print('SocketIO disconnect');
    });

    socket!.onConnect((data) {
      socket?.emit('audio', []);

      print('SocketIO connection established');
    });

    socket!.onReconnectAttempt((data) {
      print('SocketIO reconnecting');
    });

    socket!.onReconnect((data) {
      print('SocketIO reconnected!!!');
    });

    socket!.onError((e) {
      print('socketio error: $e');
    });

    socket!.connect();
  }

  void emit(String event, dynamic payload) {
    socket?.emit(event, payload);
  }
}
