import 'package:Toooony/api/index.dart';
import 'package:Toooony/controllers/socketio.dart';
import 'package:Toooony/controllers/user.dart';
import 'package:Toooony/controllers/watch.dart';
import 'package:Toooony/pages/auth/login.dart';
import 'package:Toooony/pages/profile/edit.dart';
import 'package:Toooony/utils/shared_preferences.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AuthController extends GetxController {
  var token = ''.obs;
  @override
  onInit() {
    super.onInit();
    final localStore = Get.find<LocalStorage>();

    final t = localStore.prefs.getString('token') ?? '';
    debugPrint(
        '从SharedPreferences读取到的token: ${t.isEmpty ? "空字符串" : "有token(${t.substring(0, t.length > 50 ? 50 : t.length)}...)"}');
    debugPrint('restoring auth info: $t');

    token.value = t;
    debugPrint('token.value已设置为: ${token.value.isEmpty ? "空字符串" : "有值"}');
    debugPrint('=== AuthController初始化完成 ===');
  }

  // 必须在App初始化后调用
  restore() async {
    debugPrint('=== 开始restore token ===');
    debugPrint('当前token状态: ${token.isEmpty ? "空" : "非空"}');
    if (token.isEmpty) {
      debugPrint('token为空，跳过restore');
      return;
    }

    final api = Get.find<ApiService>();

    try {
      debugPrint('尝试renewToken...');
      final res = await api.auth.renewToken();
      token.value = res.token;
      debugPrint(
          'token renewed成功: ${res.token.substring(0, res.token.length > 50 ? 50 : res.token.length)}...');
      await afterLogin();
    } catch (e) {
      debugPrint('Renew token失败: $e，执行logout');
      logout(); // 如果 renewToken 失败，注销用户
      debugPrint('Renew token failed: $e');
    }
    debugPrint('=== restore token完成 ===');
  }

  logout() async {
    debugPrint('=== 开始logout ===');
    final storage = Get.find<LocalStorage>();

    storage.prefs.clear();
    debugPrint('SharedPreferences已清空');

    token.value = '';
    debugPrint('token.value已重置为空字符串');

    final user = Get.find<UserController>();
    user.onLogout();

    final watch = Get.find<WatchController>();
    watch.onLogout();

    debugPrint('跳转到登录页面');
    Get.to(() => const LoginPage());
    debugPrint('=== logout完成 ===');
  }

  login(String t) async {
    debugPrint('=== 开始login ===');
    debugPrint(
        '接收到的token: ${t.substring(0, t.length > 50 ? 50 : t.length)}...');
    final storage = Get.find<LocalStorage>();

    debugPrint('准备写入token到SharedPreferences...');
    await storage.prefs.setString('token', t);
    debugPrint('token已写入SharedPreferences');

    // 验证写入是否成功
    final savedToken = storage.prefs.getString('token');
    debugPrint('验证写入结果: ${savedToken == t ? "写入成功" : "写入失败"}');

    token.value = t;
    debugPrint('token.value已设置');

    await afterLogin();
    debugPrint('=== login完成 ===');
  }

  afterLogin() async {
    final socket = Get.find<SocketManager>();
    socket.initSocket();

    final user = Get.find<UserController>();
    await user.init();

    final watch = Get.find<WatchController>();
    await watch.init();

    // 检查用户信息是否完备
    final userInfo = user.user.value;

    if (userInfo?.nickname.isEmpty ?? true) {
      Get.log('用户信息不完整，跳转至编辑页面');
      Get.to(() => const EditProfilePage())?.then((value) {
        if (userInfo?.carBrand.isEmpty ?? true) {
          Get.log('用户车辆信息不完整，跳转至编辑页面');
        }
      });
    }
  }
}
