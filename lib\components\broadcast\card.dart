// ignore_for_file: prefer_const_constructors, prefer_const_literals_to_create_immutables

import 'package:Toooony/components/broadcast/like.dart';
import 'package:Toooony/components/tag.dart';
import 'package:Toooony/model/broadcast.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class BroadCaseCard extends StatelessWidget {
  final BroadCastModel model;

  const BroadCaseCard({super.key, required this.model});

  Widget tags(final BuildContext context) {
    return Row(
      children: [
        ProfileTag(icon: 'female.png', content: '女生'),
        const SizedBox(
          width: 6,
        ),
        ProfileTag(icon: 'car.png', content: 'Model Y · 红'),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final width = Get.width;
    final height = width * 1.2;
    return ClipRRect(
      borderRadius: BorderRadius.circular(24),
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(24),
          image: DecorationImage(
            fit: BoxFit.cover,
            image: model.image.startsWith('http')
                ? NetworkImage(model.image)
                : AssetImage(model.image),
          ),
        ),
        child: Container(
          width: Get.width,
          height: height,
          color: const Color(0x7F747474),
          padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 30),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                model.description,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  // fontFamily: 'HongLeiZhuoShu',
                  fontWeight: FontWeight.w400,
                ),
              ),
              const SizedBox(
                height: 40,
              ),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          model.name,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 24,
                            fontFamily: 'PingFang SC',
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(
                          height: 20,
                        ),
                        tags(context),
                      ],
                    ),
                  ),
                  LikeButton(),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
