import 'package:Toooony/theme/theme.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class FieldPickerItem {
  final String label;
  final dynamic value;

  const FieldPickerItem({required this.label, required this.value});
}

class FieldsPicker extends StatefulWidget {
  const FieldsPicker({
    super.key,
    required this.items,
    required this.title,
    this.initialValue,
  });

  final List<FieldPickerItem> items;
  final String title;
  final dynamic initialValue;

  @override
  State<FieldsPicker> createState() => _FieldsPickerState();
}

class _FieldsPickerState extends State<FieldsPicker> {
  late int selectedIndex;

  @override
  void initState() {
    super.initState();
    selectedIndex =
        widget.items.indexWhere((item) => item.value == widget.initialValue);
    if (selectedIndex == -1) selectedIndex = 0;
  }

  void _onSelect(int index) {
    setState(() {
      selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = TonyTheme.of(context);
    return Container(
      width: Get.width,
      height: Get.height * 0.5,
      decoration: BoxDecoration(
        color: theme.cardBackground,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: Column(
        children: [
          const SizedBox(
            height: 20,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                widget.title,
                style: TextStyle(
                  color: theme.reversedPrimary,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              GestureDetector(
                onTap: () =>
                    Get.backLegacy(result: widget.items[selectedIndex]),
                child: Text(
                  '确定',
                  style: TextStyle(
                    color: theme.priamry,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Expanded(
            child: ListView.builder(
              itemCount: widget.items.length,
              itemBuilder: (context, index) => GestureDetector(
                onTap: () => _onSelect(index),
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 15),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border(
                      top: BorderSide(
                        color: theme.background,
                        width: 1,
                      ),
                    ),
                  ),
                  child: Row(
                    children: [
                      Text(
                        widget.items[index].label,
                        style: TextStyle(
                          color: theme.onBackground,
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      const Spacer(),
                      if (selectedIndex == index)
                        Image.asset(
                          'assets/Check-small.png',
                          width: 24,
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

Future<FieldPickerItem?> showFieldPicker(
  List<FieldPickerItem> items,
  String title, {
  dynamic initialValue,
}) async {
  return Get.bottomSheet<FieldPickerItem>(
    FieldsPicker(
      items: items,
      title: title,
      initialValue: initialValue,
    ),
    backgroundColor: Colors.transparent,
    isScrollControlled: true,
  );
}
