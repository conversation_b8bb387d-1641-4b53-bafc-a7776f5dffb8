import 'package:Toooony/api/index.dart';
import 'package:Toooony/components/button.dart';
import 'package:Toooony/pages/auth/verify.dart';
import 'package:Toooony/theme/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  String countryCode = '+86';
  final phoneController = TextEditingController(text: '');
  bool checked = false;

  @override
  void initState() {
    super.initState();
    phoneController.addListener(() {
      debugPrint('Input');
      setState(() {});
    });
  }

  @override
  void dispose() {
    phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: TonyTheme.of(context).background,
      body: SafeArea(
        child: Container(
          width: Get.width,
          padding: const EdgeInsets.symmetric(horizontal: 30),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 32,
              ),
              Image.asset(
                'assets/logo.png',
                width: 130,
              ),
              const SizedBox(
                height: 20,
              ),
              Text(
                'hello'.tr,
                style: TextStyle(
                  color: TonyTheme.of(context).onBackground,
                  fontFamily: 'D-DIN-PRO',
                  fontWeight: FontWeight.w700,
                  fontSize: 40,
                ),
              ),
              const SizedBox(
                height: 5,
              ),
              Text(
                'welcome_message'.tr,
                style: TextStyle(
                  color: TonyTheme.of(context).onBackground,
                  fontSize: 24,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(
                height: 5,
              ),
              Text(
                'auto_login_hint'.tr,
                style: TextStyle(
                  color: TonyTheme.of(context).secondary,
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                ),
              ),
              Container(
                width: Get.width,
                // padding: const EdgeInsets.symmetric(vertical: 9),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                        width: 0.5, color: TonyTheme.of(context).borderColor),
                  ),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: TextField(
                        controller: phoneController,
                        autofocus: true,
                        style: TextStyle(
                          color: TonyTheme.of(context).onBackground,
                          fontSize: 16,
                          fontFamily: 'PingFang SC',
                          fontWeight: FontWeight.w500,
                        ),
                        decoration: InputDecoration(
                          border: InputBorder.none,
                          filled: false,
                          hintText: 'phone_input_hint'.tr,
                        ),
                      ),
                    ),
                    if (phoneController.value.text.isNotEmpty)
                      GestureDetector(
                        onTap: () {
                          phoneController.clear();
                        },
                        child: SvgPicture.asset('assets/clear.svg'),
                      ),
                  ],
                ),
              ),
              const SizedBox(
                height: 32,
              ),
              Row(
                children: [
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        checked = !checked;
                      });
                    },
                    child: Icon(
                      checked ? Icons.check_circle : Icons.circle_outlined,
                      size: 20,
                      color: checked
                          ? TonyTheme.of(context).priamry
                          : TonyTheme.of(context).borderColor,
                    ),
                  ),
                  const SizedBox(
                    width: 8,
                  ),
                  Text(
                    'privacy_policy'.tr,
                    style: TextStyle(
                      color: TonyTheme.of(context).priamry,
                      fontSize: 14,
                      fontFamily: 'PingFang SC',
                      fontWeight: FontWeight.w400,
                    ),
                  )
                ],
              ),
              const SizedBox(
                height: 28,
              ),
              TonyButton(
                background: !checked || phoneController.value.text.isEmpty
                    ? TonyTheme.of(context).disabled
                    : TonyTheme.of(context).priamry,
                onPressed: () async {
                  if (!checked) {
                    return;
                  }
                  final api = Get.find<ApiService>();
                  try {
                    debugPrint(phoneController.value.text);
                    await api.auth.requestSMSCode(phoneController.value.text);
                    Get.to(
                        () => VerifyPage(
                              phone: phoneController.value.text,
                            ),
                        transition: Transition.rightToLeft);
                  } catch (e) {
                    e.printError();
                    Get.snackbar(
                      'sms_send_failed'.tr,
                      'network_retry'.tr,
                    );
                  }
                },
                text: 'verify_and_login'.tr,
              ),
              const SizedBox(
                height: 20,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
