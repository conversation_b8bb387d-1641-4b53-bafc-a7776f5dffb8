import 'package:json_annotation/json_annotation.dart';

part 'auth.g.dart';

@JsonSerializable()
class AuthToken {
  final String token;

  AuthToken({required this.token});

  factory AuthToken.fromJson(Map<String, dynamic> json) =>
      _$AuthTokenFromJson(json);
}

@JsonSerializable()
class AuthResponse {
  String username;
  String token;
  String avatar;
  List<String> roles;
  String phoneNumber;
  String wechatOpenId;
  String email;

  AuthResponse({
    this.username = '',
    this.token = '',
    this.avatar = '',
    this.roles = const [],
    this.phoneNumber = '',
    this.wechatOpenId = '',
    this.email = '',
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) =>
      _$AuthResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AuthResponseToJson(this);
}

enum CarVerifyStatus {
  NONE,
  PENDING,
  APPROVED,
  REJECTED,
}

enum Gender {
  MALE,
  FEMALE,
  OTHER,
}

@JsonSerializable()
class User {
  final int id;

  @JsonKey(defaultValue: '')
  final String nickname;

  @JsonKey(defaultValue: '')
  final String avatar;

  @JsonKey(defaultValue: '')
  final String whatsup;

  @JsonKey(defaultValue: '')
  final String email;

  @JsonKey(defaultValue: '')
  final String phoneNumber;

  @JsonKey(defaultValue: '')
  final String wechatOpenID;

  @JsonKey(defaultValue: '')
  final String wechatUnionID;

  @JsonKey(defaultValue: '')
  final String wechatNickname;

  @JsonKey(defaultValue: '')
  final String backgroundUrl;

  @JsonKey(defaultValue: '')
  final String carBrand;

  @JsonKey(defaultValue: '')
  final String carModel;

  @JsonKey(defaultValue: '')
  final String carColor;

  @JsonKey(defaultValue: Gender.OTHER)
  final Gender gender;

  @JsonKey(defaultValue: CarVerifyStatus.NONE)
  final CarVerifyStatus carVerifyStatus;

  final int? currentDeviceID;

  final DateTime createdAt;
  final DateTime updatedAt;

  User({
    required this.id,
    required this.nickname,
    required this.avatar,
    required this.whatsup,
    required this.backgroundUrl,
    required this.email,
    required this.gender,
    required this.phoneNumber,
    required this.wechatOpenID,
    required this.wechatUnionID,
    required this.wechatNickname,
    this.currentDeviceID,
    required this.createdAt,
    required this.updatedAt,
    required this.carBrand,
    required this.carModel,
    required this.carColor,
    required this.carVerifyStatus,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  get phone => null;
}
