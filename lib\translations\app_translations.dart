import 'package:get/get.dart';

class AppTranslations extends Translations {
  @override
  Map<String, Map<String, String>> get keys => {
        'en_US': {
          // Home related
          'official': 'Official',
          'ai': 'AI',
          'photo_background': 'Photo Background',
          '3D': '3D',
          'URL': 'URL',

          // 表盘库
          'watch_library': 'Watch Library',
          'Toooony官方系列': 'Toooony Official Series',
          '图片背景表盘': 'Photo Background',
          'URL类型表盘': 'URL',

          // 商店
          'hip_hop_singer': 'Hip Hop Singer',
          'western_cowboy': 'Western Cowboy',
          'astronaut': 'Astronaut',
          'toooony_hat': 'Toooony Hat',
          'surrounding_shop': 'Shop',
          'more': 'More',

          // 拍一拍
          'all_friends': 'All Friends',
          'my_pai_pai': 'My Handshakes',
          'pai_pai': 'Say Hi!',
          'pai_wo_de_ren': 'Incoming Handshakes',
          'love_jiao_you': 'Love Social',

          // 表盘编辑
          'edit_watch': 'Edit Watch',
          'cancel': 'Cancel',
          'done': 'Done',

          // Auth related
          'hello': 'Hello',
          'welcome_message': 'Welcome to Toooony\'s World',
          'auto_login_hint':
              'Unregistered phone will auto login after verification',
          'phone_input_hint': 'Please enter your phone number',
          'privacy_policy': 'Privacy Policy',
          'verify_and_login': 'Verify and Login',
          'sms_send_failed': 'Failed to send verification code',
          'network_retry': 'Please check network and try again',
          'verify_code_hint': 'Please enter the verification code',
          'auth_failed': 'Auth failed!',
          'please_signin_again': 'Please sign in again!',

          // Profile related
          'signature': 'Signature',
          'nickname_hint': 'Please enter your nickname',
          'signature_hint': 'Please enter your signature',
          'modify_failed': 'Modification failed',
          'gender': 'Gender',
          'my_order': 'My Order',
          'account_and_security': 'Account and Security',
          'feedback': 'Feedback',
          'setting': 'Setting',
          'my_car': 'My Car',

          // Settings related
          'name': 'Name',
          'confirm': 'Confirm',
          'settings': 'Settings',
          'language': 'Language',
          'theme': 'Theme',
          'hotspot_settings': 'Hotspot Settings',
          'toony_settings': 'Toony Settings',

          // Data usage related
          'data_used': 'Data Used',
          'data_limit': 'Data Limit',
          'click_to_set': 'Click to set',
          'unit_gb': 'GB',

          // Dialog related
          'error': 'Error',
          'qr_parse_failed': 'Failed to parse QR code',
          'paste_url_hint': 'Please paste URL/Shader',

          // Common UI
          'start': 'Start',
        },
        'zh_CN': {
          // Home related
          'official': '官方',
          'ai': 'AI',
          'photo_background': '照片背景',
          '3D': '3D',
          'URL': 'URL',

          // 表盘库
          'watch_library': '表盘库',
          'Toooony官方系列': 'Toooony官方系列',
          '图片背景表盘': '图片背景表盘',
          'URL类型表盘': 'URL类型表盘',

          // 商店
          'hip_hop_singer': '嘻哈歌手',
          'western_cowboy': '西部牛仔',
          'astronaut': '宇航员',
          'toooony_hat': 'Toooony帽子',
          'surrounding_shop': '周边商城',
          'more': '更多',

          // 拍一拍
          'all_friends': '全部好友',
          'my_pai_pai': '我的拍一拍',
          'pai_pai': '拍一拍',
          'pai_wo_de_ren': '拍我的人',
          'love_jiao_you': '热爱交友',

          // 我的
          'my_order': '我的订单',
          'account_and_security': '账号与安全',
          'feedback': '意见反馈',
          'setting': '设置',
          'my_car': '我的车辆',
          'logout': '登出',

          // 表盘编辑
          'edit_watch': '编辑表盘',
          'cancel': '取消',
          'done': '完成',

          // Auth related
          'hello': '你好',
          'welcome_message': '欢迎来到Toooony的世界',
          'auto_login_hint': '未注册手机验证后自动登录',
          'phone_input_hint': '请输入手机号',
          'privacy_policy': '隐私协议',
          'verify_and_login': '验证并登录',
          'sms_send_failed': '验证码发送失败',
          'network_retry': '请检查网络并重试',
          'verify_code_hint': '请输入收到的验证码',
          'auth_failed': '认证失败！',
          'please_signin_again': '请重新登录！',

          // Profile related
          'signature': '个性签名',
          'nickname_hint': '请输入你的昵称',
          'signature_hint': '请输入你的个性签名',
          'modify_failed': '修改失败',
          'gender': '性别',

          // Settings related
          'name': '名称',
          'confirm': '确定',
          'settings': '设置',
          'language': '语言',
          'theme': '主题',
          'hotspot_settings': '热点设置',
          'toony_settings': 'Toooony设置',

          // Data usage related
          'data_used': '已用流量',
          'data_limit': '流量上限',
          'click_to_set': '点击设置',
          'unit_gb': 'GB',

          // Dialog related
          'error': '错误',
          'qr_parse_failed': '无法解析二维码',
          'paste_url_hint': '请粘贴URL/Shader',

          // Common UI
          'start': '开始',
        },
      };
}
