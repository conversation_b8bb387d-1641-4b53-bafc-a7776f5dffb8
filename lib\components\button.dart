import 'package:flutter/material.dart';
import 'package:get/get.dart';

class <PERSON><PERSON>utton extends StatelessWidget {
  final String text;
  final Color background;
  final Color textColor;
  final Function onPressed;

  const TonyButton(
      {super.key,
      required this.onPressed,
      this.text = 'Start',
      this.textColor = const Color(0xff181818),
      this.background = const Color(0xFFFDE69A)});

  @override
  Widget build(BuildContext context) {
    return Material(
      color: background,
      borderRadius: BorderRadius.circular(999),
      child: InkWell(
        onTap: () {
          onPressed();
        },
        splashColor: Colors.black.withOpacity(0.3),
        borderRadius: BorderRadius.circular(999),
        child: SizedBox(
          width: double.infinity,
          height: 48,
          child: Center(
            child: Text(
              text,
              style: TextStyle(
                color: textColor,
                fontSize: 16,
                // fontFamily: 'PingFang SC',
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class <PERSON>Button extends StatelessWidget {
  final String text;
  final Function onPressed;

  const MainButton({super.key, required this.onPressed, required this.text});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onPressed();
      },
      child: Container(
        width: Get.width,
        height: 48,
        decoration: ShapeDecoration(
          gradient: const LinearGradient(
            begin: Alignment(0.00, 0.50),
            end: Alignment(1.00, 0.50),
            colors: [Color(0xFFFDE69A), Color(0xFFF2C377)],
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
        ),
        child: Center(
          child: Text(
            text,
            style: const TextStyle(
              color: Color(0xFF181818),
              fontSize: 16,
              fontFamily: 'PingFang SC',
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }
}
