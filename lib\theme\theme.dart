import 'package:flutter/material.dart';

class TonyTheme {
  Color priamry;
  Color background;
  Color onBackground;
  Color secondary;
  Color placeholder;
  Color borderColor;
  Color reversedPrimary;
  Color cardBackground;
  Color disabled;
  Color modalBackground;

  TonyTheme({
    required this.modalBackground,
    required this.priamry,
    required this.disabled,
    required this.reversedPrimary,
    required this.background,
    required this.onBackground,
    required this.secondary,
    required this.placeholder,
    required this.borderColor,
    required this.cardBackground,
  });

  static final darkTheme = TonyTheme(
    disabled: const Color(0xffFDE69A).withOpacity(0.5),
    cardBackground: const Color(0xff202020),
    reversedPrimary: Colors.white,
    background: const Color(0xff181818),
    modalBackground: const Color(0xff282828),
    borderColor: const Color(0xFF747474),
    priamry: const Color(0xffFDE69A),
    onBackground: const Color(0xffF3F3F3),
    placeholder: const Color(0xff4D4D4D),
    secondary: const Color(0xff747474),
  );

  factory TonyTheme.of(BuildContext context) {
    return darkTheme;
  }
}
