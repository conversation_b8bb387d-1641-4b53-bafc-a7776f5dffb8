import 'package:flutter/material.dart';

class ProfileTag extends StatelessWidget {
  final String icon;
  final String content;

  const ProfileTag({super.key, required this.icon, required this.content});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
      decoration: ShapeDecoration(
        color: const Color(0x33CFCFCF),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
      child: Row(
        children: [
          Image.asset(
            'assets/icons/$icon',
            width: 16,
            height: 16,
            fit: BoxFit.cover,
          ),
          const SizedBox(
            width: 5,
          ),
          Text(
            content,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontFamily: 'PingFang SC',
              fontWeight: FontWeight.w500,
            ),
          )
        ],
      ),
    );
  }
}
