import 'package:Toooony/api/index.dart';
import 'package:Toooony/controllers/auth.dart';
import 'package:Toooony/controllers/bluetooth.dart';
import 'package:Toooony/controllers/language_controller.dart';
import 'package:Toooony/controllers/socketio.dart';
import 'package:Toooony/controllers/user.dart';
import 'package:Toooony/controllers/watch.dart';
import 'package:Toooony/pages/auth/login.dart';
import 'package:Toooony/pages/broadcast/index.dart';
import 'package:Toooony/pages/home.dart';
import 'package:Toooony/pages/profile/my.dart';
import 'package:Toooony/pages/shop/index.dart';
import 'package:Toooony/pages/watch/library.dart';
import 'package:Toooony/theme/theme.dart';
import 'package:Toooony/translations/app_translations.dart';
import 'package:Toooony/utils/shared_preferences.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';

main() async {
  WidgetsFlutterBinding.ensureInitialized();

  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.light,
    ),
  );

  final localStorage = Get.put(LocalStorage());
  await localStorage.init();

  await BLEController.checkAndRequestPermissions();

  // 检查蓝牙权限
  Map<Permission, PermissionStatus> statuses = await [
    Permission.bluetooth,
    Permission.bluetoothScan,
    Permission.bluetoothConnect,
    Permission.bluetoothAdvertise,
  ].request();

  // 检查是否所有权限都已授予
  bool allBluetoothPermissionsGranted = statuses.values.every(
    (status) => status.isGranted,
  );

  if (allBluetoothPermissionsGranted) {
    Get.put(BLEController(deviceType: 'CENTRAL'));
  } else {
    Get.log('蓝牙权限未完全授予，请检查权限设置');
  }

  Get.put(AuthController());
  Get.put(LanguageController());

  Get.put(ApiService());
  Get.put(SocketManager());

  Get.put(WatchController());
  Get.put(UserController());

  Get.put(BLEController(deviceType: 'CENTRAL'));

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    final languageController = Get.find<LanguageController>();

    return GetMaterialApp(
      title: 'newToony',
      theme: ThemeData(
        useMaterial3: true,
        inputDecorationTheme: const InputDecorationTheme(
          border: InputBorder.none, // 设置输入框边框
        ),
        textSelectionTheme: TextSelectionThemeData(
          cursorColor: TonyTheme.of(context).priamry,
        ),
      ),
      // 国际化配置
      translations: AppTranslations(),
      locale: languageController.locale,
      fallbackLocale: const Locale('zh', 'CN'),
      supportedLocales: const [Locale('zh', 'CN'), Locale('en', 'US')],
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      home: const App(),
    );
  }
}

class App extends StatefulWidget {
  const App({super.key});

  @override
  _AppState createState() => _AppState();
}

final List<Widget> children = [
  const HomePage(),
  const WatchLibrary(),
  const TontShopPage(),
  const BroadCastHomePage(),
  const MyPage(),
];

class _AppState extends State<App> {
  int tab = 0;

  final authController = Get.find<AuthController>();

  @override
  void initState() {
    super.initState();
    appInit();
  }

  Future<void> requestPermissions() async {
    var status = await Permission.phone.status;
    if (!status.isGranted) {
      await Permission.phone.request();
    }
  }

  Future<void> appInit() async {
    await requestPermissions();
    // 临时调试：强制清除token，确保用户重新登录
    debugPrint('=== 应用初始化开始 ===');
    debugPrint('当前token状态: ${authController.token.value.isEmpty ? "空" : "有值"}');

    // 取消注释下面这行来强制用户重新登录
    // authController.logout();

    print(authController.token);
    if (authController.token.isEmpty) {
      debugPrint('Unauthenticated - 跳转到登录页面');
      Get.to(
        () => const LoginPage(),
        transition: Transition.rightToLeft,
        popGesture: false,
      );
    }
    await authController.restore();
    debugPrint('=== 应用初始化完成 ===');
  }

  @override
  Widget build(BuildContext context) {
    final theme = TonyTheme.of(context);
    return Scaffold(
      backgroundColor: theme.background,
      body: IndexedStack(index: tab, children: children),
      bottomNavigationBar: DecoratedBox(
        decoration: BoxDecoration(
          border: Border(
            top: BorderSide(color: theme.borderColor, width: 1),
          ), // 设置边框颜色和宽度
        ),
        child: BottomNavigationBar(
          currentIndex: tab,
          onTap: (i) => setState(() {
            tab = i;
            if (authController.token.isEmpty) {
              debugPrint('Unauthenticated');
              Get.to(
                () => const LoginPage(),
                transition: Transition.rightToLeft,
                popGesture: false,
              );
            }
          }),
          type: BottomNavigationBarType.fixed,
          // backgroundColor: theme.background,
          backgroundColor: const Color(0xB2181818),
          // useLegacyColorScheme: false,
          items: [
            BottomNavigationBarItem(
              icon: Image.asset('assets/icons/home.png', width: 30),
              activeIcon: Image.asset(
                'assets/icons/home-active.png',
                width: 30,
              ),
              label: '',
            ),
            BottomNavigationBarItem(
              icon: Image.asset('assets/icons/clock.png', width: 30),
              activeIcon: Image.asset(
                'assets/icons/clock-active.png',
                width: 30,
              ),
              label: '',
            ),
            BottomNavigationBarItem(
              icon: Image.asset('assets/icons/main.png', width: 30),
              activeIcon: Image.asset(
                'assets/icons/main-active.png',
                width: 30,
              ),
              label: '',
            ),
            BottomNavigationBarItem(
              icon: Image.asset('assets/icons/handshake.png', width: 30),
              activeIcon: Image.asset(
                'assets/icons/handshake-active.png',
                width: 30,
              ),
              label: '',
            ),
            BottomNavigationBarItem(
              icon: Image.asset('assets/icons/face.png', width: 30),
              activeIcon: Image.asset(
                'assets/icons/face-active.png',
                width: 30,
              ),
              label: '',
            ),
          ],
        ),
      ),
    );
  }
}
