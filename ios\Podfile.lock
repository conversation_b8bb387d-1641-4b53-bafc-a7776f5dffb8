PODS:
  - Flutter (1.0.0)
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - MTBBarcodeScanner (5.0.11)
  - qr_code_scanner (0.2.0):
    - Flutter
    - MTBBarcodeScanner
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - Toast (4.1.1)

DEPENDENCIES:
  - Flutter (from `Flutter`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - qr_code_scanner (from `.symlinks/plugins/qr_code_scanner/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)

SPEC REPOS:
  trunk:
    - MTBBarcodeScanner
    - Toast

EXTERNAL SOURCES:
  Flutter:
    :path: Flutter
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  qr_code_scanner:
    :path: ".symlinks/plugins/qr_code_scanner/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"

SPEC CHECKSUMS:
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  fluttertoast: e9a18c7be5413da53898f660530c56f35edfba9c
  MTBBarcodeScanner: f453b33c4b7dfe545d8c6484ed744d55671788cb
  qr_code_scanner: bb67d64904c3b9658ada8c402e8b4d406d5d796e
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  Toast: 1f5ea13423a1e6674c4abdac5be53587ae481c4e

PODFILE CHECKSUM: 819463e6a0290f5a72f145ba7cde16e8b6ef0796

COCOAPODS: 1.15.2
