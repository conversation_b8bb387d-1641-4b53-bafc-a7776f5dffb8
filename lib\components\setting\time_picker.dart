import 'package:Toooony/theme/theme.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:numberpicker/numberpicker.dart';

class TonyTimePicker extends StatefulWidget {
  const TonyTimePicker({super.key});

  @override
  State<TonyTimePicker> createState() => _TonyTimePickerState();
}

class _TonyTimePickerState extends State<TonyTimePicker> {
  int hour = 3;
  int minutes = 20;

  @override
  Widget build(BuildContext context) {
    final theme = TonyTheme.of(context);
    return Container(
      height: Get.height * 0.8,
      decoration: BoxDecoration(
        color: theme.modalBackground,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(
            12,
          ),
          topRight: Radius.circular(
            12,
          ),
        ),
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: 15,
        vertical: 22,
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              GestureDetector(
                onTap: () {
                  Get.back();
                },
                child: Text(
                  '取消',
                  style: TextStyle(
                    color: theme.priamry,
                    fontSize: 14,
                  ),
                ),
              ),
              Text(
                '编辑当前表盘',
                style: TextStyle(
                  color: theme.reversedPrimary,
                  fontSize: 17,
                ),
              ),
              Text(
                '完成',
                style: TextStyle(
                  color: theme.priamry,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 20,
          ),
          Container(
            width: Get.width,
            height: 220,
            decoration: BoxDecoration(
              color: theme.cardBackground,
              borderRadius: BorderRadius.circular(8),
            ),
            padding: const EdgeInsets.symmetric(
              vertical: 15,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    const SizedBox(
                      width: 30,
                    ),
                    SizedBox(
                      width: 100,
                      child: Center(
                        child: Text(
                          '小时',
                          style: TextStyle(
                            color: theme.secondary,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 100,
                      child: Center(
                        child: Text(
                          '分钟',
                          style: TextStyle(
                            color: theme.secondary,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(
                      width: 30,
                    ),
                  ],
                ),
                Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      const SizedBox(
                        width: 30,
                      ),
                      NumberPicker(
                        itemWidth: 100,
                        minValue: 1,
                        maxValue: 23,
                        itemHeight: 40,
                        value: hour,
                        textStyle: TextStyle(
                          color: theme.secondary,
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                        ),
                        selectedTextStyle: TextStyle(
                          color: theme.reversedPrimary,
                          fontSize: 16,
                          fontWeight: FontWeight.w400,
                        ),
                        onChanged: (h) {
                          setState(() {
                            hour = h;
                          });
                        },
                      ),
                      NumberPicker(
                        minValue: 1,
                        maxValue: 23,
                        itemHeight: 40,
                        value: minutes,
                        textStyle: TextStyle(
                          color: theme.secondary,
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                        ),
                        selectedTextStyle: TextStyle(
                          color: theme.reversedPrimary,
                          fontSize: 16,
                          fontWeight: FontWeight.w400,
                        ),
                        onChanged: (m) {
                          setState(() {
                            minutes = m;
                          });
                        },
                      ),
                      const SizedBox(
                        width: 30,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(
            height: 15,
          ),
          Container(
            width: Get.width,
            decoration: BoxDecoration(
              color: theme.cardBackground,
              borderRadius: BorderRadius.circular(8),
            ),
            padding: const EdgeInsets.symmetric(vertical: 15),
            child: Center(
              child: Text(
                '关闭自动切换',
                style: TextStyle(
                  color: theme.onBackground,
                  fontSize: 14,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
