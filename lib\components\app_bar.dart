import 'package:Toooony/theme/theme.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TonyAppBar extends StatelessWidget {
  final String middle;

  const TonyAppBar({super.key, this.middle = ''});

  @override
  Widget build(BuildContext context) {
    final theme = TonyTheme.of(context);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          GestureDetector(
            onTap: () {
              Get.back();
            },
            child: Image.asset(
              'assets/icons/arrow-back.png',
              width: 24,
              height: 24,
            ),
          ),
          Container(
            margin: const EdgeInsets.only(right: 24),
            child: Text(
              middle,
              style: TextStyle(
                color: theme.reversedPrimary,
                fontSize: 17,
                fontFamily: 'PingFang SC',
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(),
        ],
      ),
    );
  }
}
