import 'package:Toooony/components/app_bar.dart';
import 'package:Toooony/components/dialogs/loading.dart';
import 'package:Toooony/components/setting/time_picker.dart';
import 'package:Toooony/components/switch.dart';
import 'package:Toooony/controllers/watch.dart';
import 'package:Toooony/theme/theme.dart';
import 'package:Toooony/utils/dialog.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class WatchSettingsPage extends StatefulWidget {
  const WatchSettingsPage({super.key});

  @override
  State<WatchSettingsPage> createState() => _WatchSettingsPageState();
}

class _WatchSettingsPageState extends State<WatchSettingsPage> {
  final watchController = Get.find<WatchController>();
  final hotspotNameController = TextEditingController();
  final hotspotPasswordController = TextEditingController();

  @override
  void initState() {
    super.initState();
    hotspotNameController.text = watchController.hotspotName.value;
    hotspotPasswordController.text = watchController.hotspotPassword.value;
  }

  @override
  Widget build(BuildContext context) {
    final theme = TonyTheme.of(context);
    return Scaffold(
      backgroundColor: theme.background,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              const TonyAppBar(
                middle: 'Toooony设置',
              ),
              const SizedBox(
                height: 20,
              ),
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 15),
                decoration: BoxDecoration(
                  color: theme.cardBackground,
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 15),
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 15),
                      child: Row(
                        children: [
                          Text(
                            '开启LED灯',
                            style: TextStyle(
                              color: theme.onBackground,
                              fontSize: 14,
                            ),
                          ),
                          const Spacer(),
                          Obx(() {
                            return TonySwitch(
                              value: watchController.ledOn.value,
                              onChanged: (val) {
                                watchController.setLed(val);
                              },
                            );
                          }),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 20,
              ),
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 15),
                decoration: BoxDecoration(
                  color: theme.cardBackground,
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 15),
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 15),
                      child: Row(
                        children: [
                          Text(
                            'Toooony热点',
                            style: TextStyle(
                              color: theme.onBackground,
                              fontSize: 14,
                            ),
                          ),
                          const Spacer(),
                          Obx(() {
                            return TonySwitch(
                              value: watchController.hotspotOn.value,
                              onChanged: (val) {
                                watchController.setHotspot(val);
                              },
                            );
                          }),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 15),
                      child: Row(
                        children: [
                          Text(
                            '名称',
                            style: TextStyle(
                              color: theme.onBackground,
                              fontSize: 14,
                            ),
                          ),
                          Expanded(
                            child: Container(
                              height: 40,
                              alignment: Alignment.centerRight,
                              child: TextField(
                                controller: hotspotNameController,
                                textAlign: TextAlign.right,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 14,
                                  fontFamily: 'D-DIN-PRO',
                                  fontWeight: FontWeight.w500,
                                ),
                                decoration: const InputDecoration(
                                  border: InputBorder.none,
                                  contentPadding: EdgeInsets.zero,
                                  isDense: true,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 15),
                      child: Row(
                        children: [
                          Text(
                            '密码',
                            style: TextStyle(
                              color: theme.onBackground,
                              fontSize: 14,
                            ),
                          ),
                          Expanded(
                            child: Container(
                              height: 40,
                              alignment: Alignment.centerRight,
                              child: TextField(
                                controller: hotspotPasswordController,
                                textAlign: TextAlign.right,
                                obscureText: true,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 14,
                                  fontFamily: 'D-DIN-PRO',
                                  fontWeight: FontWeight.w500,
                                ),
                                decoration: const InputDecoration(
                                  border: InputBorder.none,
                                  contentPadding: EdgeInsets.zero,
                                  isDense: true,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 20,
              ),
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 15),
                decoration: BoxDecoration(
                  color: theme.cardBackground,
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 15),
                child: Column(
                  children: [
                    GestureDetector(
                      onTap: () {
                        Get.bottomSheet(
                          const TonyTimePicker(),
                          isScrollControlled: true,
                        );
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 15),
                        decoration: BoxDecoration(
                          color: Colors.transparent,
                          border: Border(
                            bottom: BorderSide(
                              width: 1.5,
                              color: theme.background,
                            ),
                          ),
                        ),
                        child: Row(
                          children: [
                            Text(
                              '自动切换',
                              style: TextStyle(
                                color: theme.onBackground,
                                fontSize: 14,
                              ),
                            ),
                            const Spacer(),
                            Text(
                              '关闭',
                              style: TextStyle(
                                color: theme.secondary,
                                fontSize: 14,
                                fontFamily: 'D-DIN-PRO',
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Image.asset(
                              'assets/Right.png',
                              width: 24,
                              height: 24,
                            ),
                          ],
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 15),
                      child: Row(
                        children: [
                          Text(
                            '拍一拍',
                            style: TextStyle(
                              color: theme.onBackground,
                              fontSize: 14,
                            ),
                          ),
                          const Spacer(),
                          const TonySwitch(),
                        ],
                      ),
                    ),
                    Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        '轻拍设备切换表盘',
                        style: TextStyle(
                          color: theme.secondary,
                          fontSize: 12,
                          fontFamily: 'PingFang SC',
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    //当前设备SN码
                    Align(
                      alignment: Alignment.centerLeft,
                      child: Obx(() {
                        final currentDevice = watchController.device.value;
                        final serial = currentDevice?.serial ?? "未知设备";

                        return RichText(
                          text: TextSpan(
                            text: '设备SN码  ',
                            style: TextStyle(
                              color: theme.onBackground,
                              fontSize: 14,
                            ),
                            children: [
                              TextSpan(
                                text: serial,
                                style: TextStyle(
                                  color: theme.onBackground,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        );
                      }),
                    ),
                    const SizedBox(
                      height: 15,
                    ),
                    GestureDetector(
                      onTap: () async {
                        final confirmed = await showConfirmDialog(
                          Get.context!,
                          title: '还原当前设备???',
                          content: '还原后不可撤销',
                        );

                        if (!confirmed) {
                          return;
                        }

                        showLoadingDialog();

                        await watchController.unbindCurrentDevice();

                        Get.closeAllDialogs();

                        Get.back();
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 15),
                        decoration: BoxDecoration(
                          color: Colors.transparent,
                          border: Border(
                            bottom: BorderSide(
                              width: 1.5,
                              color: theme.background,
                            ),
                          ),
                        ),
                        child: Row(
                          children: [
                            Text(
                              '设备还原',
                              style: TextStyle(
                                color: theme.onBackground,
                                fontSize: 14,
                              ),
                            ),
                            const Spacer(),
                            Text(
                              '还原后为无主设备',
                              style: TextStyle(
                                color: theme.secondary,
                                fontSize: 14,
                                fontFamily: 'D-DIN-PRO',
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
