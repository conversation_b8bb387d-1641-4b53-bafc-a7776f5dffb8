import 'package:Toooony/components/button.dart';
import 'package:Toooony/theme/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

class EditURLDialog extends StatefulWidget {
  const EditURLDialog({super.key});

  @override
  State<EditURLDialog> createState() => _EditURLDialogState();
}

class _EditURLDialogState extends State<EditURLDialog> {
  final TextEditingController _controller = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final theme = TonyTheme.of(context);
    return Center(
      child: Material(
        color: Colors.transparent,
        child: Container(
          width: Get.width * 0.8,
          height: 200,
          padding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 30,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.0),
            color: theme.cardBackground,
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: theme.borderColor,
                        width: 0.24,
                      ),
                    ),
                  ),
                  width: Get.width,
                  child: Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: _controller,
                          style: const TextStyle(
                            color: Colors.white,
                          ),
                          decoration: const InputDecoration(
                            filled: false,
                            hintText: '请粘贴URL/Shader',
                          ),
                        ),
                      ),
                      // GestureDetector(
                      //   onTap: () async {
                      //     final data = await Clipboard.getData('text/plain');
                      //     _controller.text = data?.text ?? '';
                      //   },
                      //   child: const Text(
                      //     '粘贴',
                      //     style: TextStyle(
                      //       color: Color(0xFFFDE69A),
                      //       fontSize: 16,
                      //       fontFamily: 'PingFang SC',
                      //       fontWeight: FontWeight.w400,
                      //       decoration: TextDecoration.underline,
                      //       decorationColor: Color(0xFFFDE69A),
                      //     ),
                      //   ),
                      // )
                    ],
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
                MainButton(
                  onPressed: () {
                    Get.backLegacy(result: _controller.text.trim());
                  },
                  text: '确定',
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

Future<String?> showEditURLDialog(BuildContext context) async {
  return Get.dialog<String>(
    const EditURLDialog(),
  );
}
