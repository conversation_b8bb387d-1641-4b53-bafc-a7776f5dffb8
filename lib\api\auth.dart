import 'package:Toooony/api/index.dart';
import 'package:Toooony/model/auth.dart';

class AuthApi {
  final Api api;

  AuthApi(this.api);

  Future<AuthToken> renewToken() =>
      api.post('/auth/renew', {}).then((res) => AuthToken.fromJson(res.body));

  Future<void> requestSMSCode(String phoneNumber) async =>
      api.post('/auth/sms/logincode', {'phoneNumber': phoneNumber});

  Future<String> getTicketBySMS(String phoneNumber, String code) async =>
      api.post('/auth/ticket', {
        'type': 'sms',
        'phoneNumber': phoneNumber,
        'code': code
      }).then((value) => value.body as String);

  Future<User> getUserInfo() =>
      api.get('/user/info').then((value) => User.fromJson(value.body));

  Future<User> updateUserInfo({
    String? nickname,
    String? whatsup,
    Gender? gender,
    String? avatar,
    String? backgroundUrl,
    String? carBrand,
    String? carModel,
    String? carColor,
  }) =>
      api.put('/user/profile', {
        if (nickname != null) "nickname": nickname,
        if (whatsup != null) "whatsup": whatsup,
        if (gender != null) "gender": gender.name,
        if (avatar != null) "avatar": avatar,
        if (backgroundUrl != null) "backgroundUrl": backgroundUrl,
        if (carBrand != null) "carBrand": carBrand,
        if (carModel != null) "carModel": carModel,
        if (carColor != null) "carColor": carColor,
      }).then((value) => User.fromJson(value.body));

  Future<AuthResponse> getJwtToken(
    String ticket,
    bool fastLogin,
  ) async =>
      api.post('/auth/token', {'ticket': ticket, 'fast_login': fastLogin}).then(
          (value) => AuthResponse.fromJson(value.body));

  Future<String> bindDevice(String sn, String encStr, int time) async {
    // 参数验证
    if (sn.isEmpty || encStr.isEmpty || time <= 0) {
      throw Exception('设备绑定参数无效：序列号、加密字符串或时间戳不能为空');
    }

    // 验证时间戳是否合理（不能太久之前或太久之后）
    final now = DateTime.now().millisecondsSinceEpoch;
    final timeDiff = (time - now).abs();
    if (timeDiff > 24 * 60 * 60 * 1000) {
      // 24小时
      print('警告：时间戳与当前时间差距较大 - 当前：$now，请求：$time，差距：${timeDiff}ms');
    }

    // 重试配置
    const maxRetries = 3;
    const retryDelay = Duration(seconds: 2);

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      // 打印详细的请求信息
      print('=== 设备绑定请求详细信息 (尝试 $attempt/$maxRetries) ===');
      print('请求URL: ${api.httpClient.baseUrl}/device/bind');
      print('请求方法: POST');
      print(
          '当前JWT Token: ${api.authController.token.value.isEmpty ? "无Token" : "有Token(${api.authController.token.value.substring(0, 50)}...)"}');
      print('请求参数:');
      print('  - serial: $sn');
      print('  - encStr: $encStr');
      print('  - time: $time');
      print('请求时间: ${DateTime.now().toIso8601String()}');

      final requestData = {
        'serial': sn,
        'encStr': encStr,
        'time': time,
      };

      print('完整请求体: $requestData');
      print('========================');

      try {
        final response = await api.post('/device/bind', requestData);

        // 打印响应详细信息
        print('=== 设备绑定响应详细信息 ===');
        print('响应状态码: ${response.statusCode}');
        print('响应体: ${response.body}');
        print('响应头: ${response.headers}');
        print('响应时间: ${DateTime.now().toIso8601String()}');
        print('========================');

        return response.body as String;
      } catch (error) {
        // 打印错误详细信息
        print('=== 设备绑定请求错误 (尝试 $attempt/$maxRetries) ===');
        print('错误信息: $error');
        print('错误类型: ${error.runtimeType}');
        print('错误时间: ${DateTime.now().toIso8601String()}');

        // 检查是否是认证相关错误
        String errorMessage = error.toString();
        print('完整错误消息: $errorMessage');

        if (errorMessage.contains('401') ||
            errorMessage.contains('Unauthorized')) {
          print('检测到认证失败错误 - JWT Token可能无效或已过期');
          print('=====================');
          throw Exception('用户认证失败，请重新登录后再试。');
        } else if (errorMessage.contains('Unique constraint failed') ||
            errorMessage.contains('userID') ||
            errorMessage.contains('P2002')) {
          print('检测到用户设备绑定冲突错误 - 用户已绑定设备');
          print('=====================');
          throw Exception('您已绑定其他设备，每个用户只能绑定一个设备。请先在设备管理中解绑现有设备。');
        } else if (errorMessage.contains('Device already bound') ||
            errorMessage.contains('设备已被绑定')) {
          print('检测到设备已被绑定错误');
          print('=====================');
          throw Exception('该设备已被其他用户绑定，请检查设备序列号或联系客服处理。');
        } else if (errorMessage.contains('500') ||
            errorMessage.contains('Internal server error')) {
          print('检测到服务器内部错误');

          // 如果还有重试机会且是500错误，则重试
          if (attempt < maxRetries) {
            print('等待 ${retryDelay.inSeconds} 秒后重试...');
            print('=====================');
            await Future.delayed(retryDelay);
            continue; // 继续下一次重试
          } else {
            print('已达到最大重试次数，放弃重试');
            print('=====================');
            throw Exception('服务器内部错误，已重试 $maxRetries 次仍然失败。请稍后重试或联系客服。');
          }
        } else {
          print('其他类型的绑定错误');
          print('=====================');
          throw Exception('设备绑定失败：$errorMessage');
        }
      }
    }

    // 理论上不会到达这里，但为了代码完整性
    throw Exception('设备绑定失败：未知错误');
  }
}
