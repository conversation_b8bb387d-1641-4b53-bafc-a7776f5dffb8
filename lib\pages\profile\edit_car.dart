import 'package:Toooony/components/button.dart';
import 'package:Toooony/theme/theme.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class EditCarPage extends StatefulWidget {
  const EditCarPage({super.key});

  @override
  State<EditCarPage> createState() => _EditCarPageState();
}

class _EditCarPageState extends State<EditCarPage> {
  final nameController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final theme = TonyTheme.of(context);
    return Scaffold(
      backgroundColor: theme.background,
      resizeToAvoidBottomInset: false,
      body: SafeArea(
        child: Container(
          width: Get.width,
          padding: const EdgeInsets.symmetric(horizontal: 30),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 20,
              ),
              Image.asset(
                'assets/logo.png',
                width: 130,
              ),
              const SizedBox(
                height: 20,
              ),
              Text(
                '完善个人信息',
                style: TextStyle(
                  color: TonyTheme.of(context).onBackground,
                  fontSize: 24,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(
                height: 50,
              ),
              Align(
                alignment: Alignment.center,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Image.asset(
                      'assets/user-anonymous.png',
                      width: 80,
                    ),
                    const SizedBox(
                      height: 5,
                    ),
                    Text(
                      '点击设置头像',
                      style: TextStyle(
                        color: theme.secondary,
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 20,
              ),
              Text(
                '昵称',
                style: TextStyle(
                  color: theme.reversedPrimary,
                  fontSize: 14,
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              Container(
                width: Get.width,
                // padding: const EdgeInsets.symmetric(vertical: 9),
                decoration: BoxDecoration(
                    border: Border(
                        bottom: BorderSide(
                            width: 0.24,
                            color: TonyTheme.of(context).borderColor))),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: TextField(
                        controller: nameController,
                        style: TextStyle(
                          color: TonyTheme.of(context).onBackground,
                          fontSize: 16,
                          fontFamily: 'PingFang SC',
                          fontWeight: FontWeight.w500,
                        ),
                        decoration: const InputDecoration(
                          border: InputBorder.none, // 取消边框
                          filled: false, // 不填充背景色
                          hintText: '请输入你的昵称',
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 40,
              ),
              Text(
                '性别',
                style: TextStyle(
                  color: theme.reversedPrimary,
                  fontSize: 14,
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 8),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: theme.cardBackground,
                      ),
                      child: Row(
                        children: [
                          Image.asset(
                            'assets/male.png',
                            width: 24,
                          ),
                          const SizedBox(
                            width: 8,
                          ),
                          Text(
                            '男',
                            style: TextStyle(
                              color: theme.secondary,
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                          Expanded(child: Container()),
                          Image.asset(
                            'assets/check-one.png',
                            width: 24,
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(
                    width: 15,
                  ),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 8),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: theme.cardBackground,
                      ),
                      child: Row(
                        children: [
                          Image.asset(
                            'assets/female.png',
                            width: 24,
                          ),
                          const SizedBox(
                            width: 8,
                          ),
                          Text(
                            '女',
                            style: TextStyle(
                              color: theme.secondary,
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                          Expanded(child: Container()),
                          Image.asset(
                            'assets/check-one.png',
                            width: 24,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              Expanded(child: Container()),
              TonyButton(
                text: '确认',
                onPressed: () {},
              ),
              const SizedBox(
                height: 40,
              )
            ],
          ),
        ),
      ),
    );
  }
}
