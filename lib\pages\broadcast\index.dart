// ignore_for_file: prefer_const_constructors, prefer_const_literals_to_create_immutables

import 'package:Toooony/components/broadcast/swiper.dart';
import 'package:Toooony/theme/theme.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class BroadCastHomePage extends StatefulWidget {
  const BroadCastHomePage({super.key});

  @override
  State<BroadCastHomePage> createState() => _BroadCastHomePageState();
}

class _BroadCastHomePageState extends State<BroadCastHomePage> {
  @override
  Widget build(BuildContext context) {
    final theme = TonyTheme.of(context);

    Widget bottom(final BuildContext context) {
      return Container(
        decoration: BoxDecoration(
          color: theme.cardBackground,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          children: [
            GestureDetector(
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 15,
                  vertical: 13,
                ),
                child: Row(children: [
                  Image.asset(
                    'assets/icons/friends.png',
                    width: 30,
                  ),
                  const SizedBox(
                    width: 6,
                  ),
                  Text(
                    'all_friends'.tr,
                    style: TextStyle(
                      color: theme.onBackground,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  const Spacer(),
                  Image.asset(
                    'assets/Right.png',
                    width: 24,
                  ),
                ]),
              ),
            ),
            GestureDetector(
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 15,
                  vertical: 13,
                ),
                child: Row(children: [
                  Image.asset(
                    'assets/icons/id_card.png',
                    width: 30,
                  ),
                  const SizedBox(
                    width: 6,
                  ),
                  Text(
                    'my_pai_pai'.tr,
                    style: TextStyle(
                      color: theme.onBackground,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  const Spacer(),
                  Image.asset(
                    'assets/Right.png',
                    width: 24,
                  ),
                ]),
              ),
            )
          ],
        ),
      );
    }

    return Scaffold(
      backgroundColor: theme.background,
      body: SafeArea(
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: 15,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 24,
              ),
               Text(
                'pai_pai'.tr,
                style: TextStyle(
                  color: Color(0xFFF3F3F3),
                  fontSize: 34,
                  fontFamily: 'PingFang SC',
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(
                height: 20,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'pai_wo_de_ren'.tr,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontFamily: 'PingFang SC',
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                      'love_jiao_you'.tr,
                    style: TextStyle(
                      color: const Color(0xFF747474) /* 内容文字1 */,
                      fontSize: 14,
                      fontFamily: 'PingFang SC',
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
              const SizedBox(
                height: 12,
              ),
              BroadcastSwiper(
                // cards: cards,
              ),
              const SizedBox(
                height: 40,
              ),
              bottom(context),
            ],
          ),
        ),
      ),
    );
  }
}
