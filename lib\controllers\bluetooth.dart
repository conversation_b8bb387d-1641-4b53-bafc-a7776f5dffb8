import 'dart:convert';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:ble_peripheral/ble_peripheral.dart' as peripheral;
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:crypto/crypto.dart';
import 'ble_handlers/ble_message_handler.dart';

/* 暴露的方法:

// 中心设备模式(手机)
  1. 连接设备：connectDevice
  2. 断开连接：disconnectDevice
  3. 开始扫描：startScan
  4. 停止扫描：stopScan
  5. 发现服务：discoverServices
  6. 订阅服务：subscribeService
  7. 发送消息：sendMessage（MessageType type, dynamic content）

// 外设模式(TOOOONY)
  1. 添加服务：addService
  2. 开启广播：startAdvertising
  3. 停止广播：stopAdvertising
  4. 发送消息：sendMessage（MessageType type, dynamic content）

*/

/* 暴露的变量：
// 蓝牙本身
  isBleOn

// 外设（TOOOONY）
    isAdvertising
    subscribedDevices

// 中心设备（手机）
    isConnected
    connectedDevice
    connectedDeviceFromQRScan

    isScanning
    scanResults
*/

/* 对于不同的设备（中心设备、外设），消息处理方式不同_handleMessage不同
*/

// 消息处理类型枚举
enum MessageType {
  activate('ACTIVATE'),
  wifi('WIFI'),
  chat('CHAT'),
  hotspot('HOTSPOT'),
  system('SYSTEM');

  final String value;
  const MessageType(this.value);
}

class BLEController extends GetxController {
  // 设备类型
  final String deviceType;

  // BLE是否开启
  RxBool isBleOn = false.obs;

  // 是否已连接
  RxBool isConnected = false.obs;

  // ============= 外设模式相关变量 =============
  // 是否正在广播
  RxBool isAdvertising = false.obs;
  // 已订阅的设备列表
  RxList<String> subscribedDevices = <String>[].obs;

  // ============= 中心设备模式相关变量 =============
  // 当前连接的设备
  Rx<BluetoothDevice?> connectedDevice = Rx<BluetoothDevice?>(null);
  Rx<BluetoothDevice?> connectedDeviceFromQRScan = Rx<BluetoothDevice?>(null);
  // 发现的服务列表
  RxList<BluetoothService> discoveredServices = <BluetoothService>[].obs;
  RxBool isScanning = false.obs;
  RxList<ScanResult> scanResults = <ScanResult>[].obs;

  // ============= 设备标识 =============
  // 设备名称
  String deviceName = "TOOOONY";

  // ============= BLE服务UUID =============
  // 读写服务UUID：该服务用于外围设备广播，中心设备连接，并进行数据交换，
  String serviceUUID = "0000180D-0000-1000-8000-00805F9B34FB";
  String serviceUUIDShort = "180D";
  // 读写特征值UUID
  String characteristicUUID = "00002A18-0000-1000-8000-00805F9B34FB";
  String characteristicUUIDShort = "2A18";

  // 用于在app端连接的UUID
  String deviceConnectionUUID = "db741f0a-3717-11f0-8def-a8a159dc2c92";

  // 监听器
  StreamSubscription? _connectionSubscription;
  Timer? _deviceCheckTimer;

  // 是否已经初始化
  RxBool hasInitialized = false.obs;

  // 消息处理器
  final BLEMessageHandler _messageHandler = BLEMessageHandler();

  BLEController({required this.deviceType});

  // 检查并请求蓝牙权限
  static Future<bool> checkAndRequestPermissions() async {
    try {
      // 检查蓝牙权限
      var bluetoothStatus = await Permission.bluetooth.status;
      var bluetoothScanStatus = await Permission.bluetoothScan.status;
      var bluetoothConnectStatus = await Permission.bluetoothConnect.status;
      var bluetoothAdvertiseStatus = await Permission.bluetoothAdvertise.status;
      var locationStatus = await Permission.location.status;

      // 如果任何权限被拒绝，请求权限
      if (bluetoothStatus.isDenied) {
        bluetoothStatus = await Permission.bluetooth.request();
      }
      if (bluetoothScanStatus.isDenied) {
        bluetoothScanStatus = await Permission.bluetoothScan.request();
      }
      if (bluetoothConnectStatus.isDenied) {
        bluetoothConnectStatus = await Permission.bluetoothConnect.request();
      }
      if (bluetoothAdvertiseStatus.isDenied) {
        bluetoothAdvertiseStatus =
            await Permission.bluetoothAdvertise.request();
      }
      if (locationStatus.isDenied) {
        locationStatus = await Permission.location.request();
      }

      // 检查是否所有权限都已授予
      return bluetoothStatus.isGranted &&
          bluetoothScanStatus.isGranted &&
          bluetoothConnectStatus.isGranted &&
          bluetoothAdvertiseStatus.isGranted &&
          locationStatus.isGranted;
    } catch (e) {
      Get.log("检查权限时出错: $e");
      return false;
    }
  }

  @override
  void onInit() {
    // 根据设备类型设置不同的回调
    Get.log("deviceType " + deviceType);

    _setupBluetoothStateListener();
    _messageHandler.initialize();

    super.onInit();
  }

  Future<void> _periperalInitialize() async {
    try {
      // 初始化BLE外设 - 这是必须的
      await peripheral.BlePeripheral.initialize();

      // 检查BLE是否支持
      bool isSupported = await peripheral.BlePeripheral.isSupported();
      if (!isSupported) {
        return;
      }
    } catch (e) {
      Get.log("初始化时出错: $e");
    }
  }

  // 设置外围设备回调
  void _setupPeripheralCallbacks() {
    Get.log("_setupPeripheralCallbacks");
    // 设置BLE状态变化回调
    peripheral.BlePeripheral.setBleStateChangeCallback((bool state) async {
      isBleOn.value = state;
      Get.log("BLE状态变化: $state");
    });

    // 设置广播状态变化回调
    peripheral.BlePeripheral.setAdvertisingStatusUpdateCallback((
      bool advertising,
      String? error,
    ) {
      Get.log("广播状态变化: $advertising, 错误: $error");
    });

    // 设置连接状态变化回调
    peripheral.BlePeripheral.setConnectionStateChangeCallback((
      String deviceId,
      bool connected,
    ) async {
      if (connected) {
        subscribedDevices.add(deviceId);
        isConnected.value = true;
      } else {
        subscribedDevices.remove(deviceId);
        isConnected.value = false;
      }
    });

    // 设置配对状态变化回调
    peripheral.BlePeripheral.setBondStateChangeCallback((
      String deviceId,
      peripheral.BondState state,
    ) {
      Get.log("配对状态变化: $deviceId, 状态: $state");
      // 如果配对失败或取消，停止定时器
      if (state == peripheral.BondState.none) {
        _stopAllSubscriptions();
      }
    });

    // 设置特征值写入回调
    peripheral.BlePeripheral.setWriteRequestCallback((
      String deviceId,
      String characteristicId,
      int offset,
      Uint8List? value,
    ) {
      try {
        if (value == null) {
          return null;
        }
        // 解码接收到的消息
        final message = utf8.decode(value);

        // 解析消息格式
        try {
          final Map<String, dynamic> messageData = json.decode(message);
          final String sender = messageData['sender'] ?? 'unknown';
          final String type = messageData['type'] ?? '';
          final dynamic content = messageData['content'] ?? '';
          // 处理消息
          _handleMessage(sender, type, content);
        } catch (e) {
          Get.log("消息格式解析错误: $e");
        }

        return null;
      } catch (e) {
        return null;
      }
    });
  }

  // 设置蓝牙状态监听
  void _setupBluetoothStateListener() {
    FlutterBluePlus.adapterState.listen((state) async {
      isBleOn.value = state == BluetoothAdapterState.on;

      if (!isBleOn.value) {
        // 蓝牙关闭时清理所有状态
        isAdvertising.value = false;
        isConnected.value = false;

        // 断开连接
        if (connectedDevice.value != null) {
          await connectedDevice.value!.disconnect();
          connectedDevice.value = null;
        }
        if (connectedDeviceFromQRScan.value != null) {
          await connectedDeviceFromQRScan.value!.disconnect();
          connectedDeviceFromQRScan.value = null;
        }

        // 清空所有列表
        subscribedDevices.clear();
        discoveredServices.clear();

        // 停止所有订阅
        await _stopAllSubscriptions();

        // 停止广播
        if (isAdvertising.value) {
          await stopAdvertising();
        }

        // 取消设备检查定时器
        _deviceCheckTimer?.cancel();
        _deviceCheckTimer = null;
      } else {
        // 如果蓝牙打开，显示提示
        // if (deviceType == "PERIPHERAL") {
        //   _periperalInitialize();
        //   _setupPeripheralCallbacks();
        // } else {
        //   startScan();
        // }
        hasInitialized.value = true;
      }
    });
  }

  // 开始监听设备连接状态
  void _startDeviceConnectionListener() {
    Get.log("_startDeviceConnectionListener");
    // 如果已经有定时器在运行，先取消它
    _deviceCheckTimer?.cancel();

    // 定期检查已连接的设备
    _deviceCheckTimer = Timer.periodic(const Duration(seconds: 2), (
      timer,
    ) async {
      try {
        List<BluetoothDevice> devices = await FlutterBluePlus.systemDevices([]);
        Get.log("检查已连接设备: ${devices.length}个设备");

        // 检查是否有包含目标UUID的设备
        for (var device in devices) {
          try {
            List<BluetoothService> services = await device.discoverServices();
            for (var service in services) {
              Get.log(service.uuid.toString());
            }

            for (var service in services) {
              if (service.uuid.toString().toLowerCase() ==
                  deviceConnectionUUID.toLowerCase()) {
                Get.log("找到包含目标UUID的设备: ${device.remoteId}");
                for (var characteristic in service.characteristics) {
                  if (characteristic.uuid.toString().toLowerCase() ==
                      characteristicUUID.toLowerCase()) {
                    List<int> value = await characteristic.read();
                    String serial = utf8.decode(value);
                    Get.log("读取到设备 SN 码: $serial");
                  }
                }

                // 如果找到目标设备且当前未连接，则建立连接
                if (!isConnected.value ||
                    connectedDevice.value?.remoteId != device.remoteId) {
                  await device.connect();
                }
                return;
              }
            }
          } catch (e) {
            Get.log("检查设备服务时出错: $e");
          }
        }
        if (isConnected.value && connectedDevice.value != null) {
          bool found = false;
          for (var device in devices) {
            if (device.remoteId == connectedDevice.value?.remoteId) {
              found = true;
              break;
            }
          }
          // 如果没有找到目标设备，且当前是连接状态，则断开连接
          if (!found) {
            Get.log("目标设备已断开连接");
            await disconnectDevice();
          }
        }
      } catch (e) {
        Get.log("检查设备连接状态时出错: $e");
      }
    });
  }

  // 停止所有订阅
  Future<void> _stopAllSubscriptions() async {
    await _connectionSubscription?.cancel();
    _connectionSubscription = null;
  }

  // 修改特征值通知处理
  Future<void> _setNotify(
    BluetoothCharacteristic characteristic,
    bool enable,
  ) async {
    try {
      await characteristic.setNotifyValue(enable);
      if (enable) {
        // 取消之前的监听
        _connectionSubscription?.cancel();
        // 监听特征值变化
        _connectionSubscription = characteristic.lastValueStream.listen((
          value,
        ) async {
          if (value.isNotEmpty) {
            try {
              final message = utf8.decode(value);
              // 解析消息
              try {
                final Map<String, dynamic> messageData = json.decode(message);
                final String sender = messageData['sender'] ?? '';
                final String type = messageData['type'] ?? '';
                final dynamic content = messageData['content'] ?? '';

                _handleMessage(sender, type, content);

                // 如果是来自CENTRAL的消息，直接返回
                if (sender == deviceType) {
                  Get.log("过滤掉来自自己的消息");
                  return;
                }
                Get.log("收到消息: $message");
              } catch (e) {
                Get.log("消息解析错误: $e");
              }
            } catch (e) {
              Get.log("消息处理错误: $e");
            }
          }
        });
      } else {
        _connectionSubscription?.cancel();
      }
    } catch (e) {
      Get.log("设置通知失败: $e");
    }
  }

  // 将 serial 转换为 UUID
  String _stringToUUID(String str) {
    // 使用 MD5 生成哈希
    final bytes = utf8.encode(str);
    final digest = md5.convert(bytes);
    final hash = digest.toString();

    // 将哈希转换为 UUID 格式 (8-4-4-4-12)
    return '${hash.substring(0, 8)}-${hash.substring(8, 12)}-${hash.substring(12, 16)}-${hash.substring(16, 20)}-${hash.substring(20, 32)}';
  }

  // 添加服务
  Future<void> addServices(String? serial) async {
    try {
      if (serial == null || serial.isEmpty) {
        throw Exception("SN码不能为空");
      }

      // 生成 UUID
      final serialUUID = _stringToUUID(serial);
      Get.log("生成的 UUID: $serialUUID");

      // 先清除所有服务
      await peripheral.BlePeripheral.clearServices();
      Get.log("已清除所有服务");

      // 添加主服务（包含读写和通知功能）
      final service = peripheral.BleService(
        uuid: serviceUUID,
        primary: true,
        characteristics: [
          peripheral.BleCharacteristic(
            uuid: characteristicUUID,
            properties: [1, 3, 4], // read, write, notify
            permissions: [0, 1], // readable, writeable
          ),
        ],
      );
      // 添加设备连接服务
      final connectionService = peripheral.BleService(
        uuid: deviceConnectionUUID,
        primary: true,
        characteristics: [
          peripheral.BleCharacteristic(
            uuid: characteristicUUID,
            properties: [1], // read, write, notify
            permissions: [0], // readable, writeable
          ),
        ],
      );
      // 添加 serial 服务（使用生成的 UUID）
      final serialService = peripheral.BleService(
        uuid: serialUUID,
        primary: true,
        characteristics: [
          peripheral.BleCharacteristic(
            uuid: characteristicUUID,
            properties: [1], // read
            permissions: [0], // readable
          ),
        ],
      );

      // 添加服务（带重试机制）
      int retryCount = 0;
      const maxRetries = 3;
      bool success = false;

      while (!success && retryCount < maxRetries) {
        try {
          Get.log("尝试添加服务 (尝试 ${retryCount + 1}/$maxRetries)");

          // 先添加主服务
          await peripheral.BlePeripheral.addService(service);
          Get.log("主服务添加成功");
          // 等待一小段时间
          await Future.delayed(const Duration(milliseconds: 100));
          // 再添加设备连接服务
          await peripheral.BlePeripheral.addService(connectionService);
          Get.log("设备连接服务添加成功");
          // 等待一小段时间
          await Future.delayed(const Duration(milliseconds: 100));
          // 再添加 serial 服务
          await peripheral.BlePeripheral.addService(serialService);
          Get.log("Serial 服务添加成功");

          success = true;
          Get.log("所有服务添加成功");
        } catch (e) {
          retryCount++;
          Get.log("添加服务失败 (尝试 $retryCount/$maxRetries): $e");

          if (retryCount < maxRetries) {
            // 等待一段时间后重试
            await Future.delayed(const Duration(milliseconds: 500));
            // 清除服务后重试
            await peripheral.BlePeripheral.clearServices();
          } else {
            throw Exception("添加服务失败，已重试 $maxRetries 次");
          }
        }
      }
    } catch (e) {
      Get.log("添加服务时出错: $e");
      rethrow; // 重新抛出异常，让调用者知道发生了错误
    }
  }

  // 修改广播方法
  Future<void> startAdvertising() async {
    try {
      // 开始广播，包含 SN 码
      await peripheral.BlePeripheral.startAdvertising(
        services: [serviceUUID], // 只广播一个服务UUID
        localName: deviceName,
      );
      Get.log("开始广播成功: ${serviceUUID}");
      isAdvertising.value = true;
    } catch (e) {
      Get.log("开始广播时出错: $e");
    }
  }

  // 停止广播
  Future<void> stopAdvertising() async {
    try {
      if (isConnected.value) {
        return;
      }
      await peripheral.BlePeripheral.clearServices();
      await peripheral.BlePeripheral.stopAdvertising();
      isAdvertising.value = false;
    } catch (e) {}
  }

  // 开始扫描设备（暂时扫描2s）
  Future<List<ScanResult>> startScan({
    bool isFromActivate = false,
    List<String>? bindedDevicesSerials,
    Function()? onSuccess,
  }) async {
    if (isScanning.value) return [];

    Get.log("开始扫描设备 - 模式: ${isFromActivate ? '通过UUID扫描' : '通过序列号扫描'}");

    // 检查并请求权限
    bool hasPermissions = await checkAndRequestPermissions();
    if (!hasPermissions) {
      Get.log("缺少必要的蓝牙权限，无法开始扫描");
      return [];
    }

    isScanning.value = true;
    // 只在开始扫描时清空一次
    scanResults.clear();

    try {
      // 开始扫描
      await FlutterBluePlus.startScan(
        androidScanMode: AndroidScanMode.lowLatency,
      );

      List<String> scanResultsRemoteIds = [];
      Completer<List<ScanResult>> completer = Completer();

      // 监听扫描结果
      FlutterBluePlus.scanResults.listen((results) async {
        if (results.isNotEmpty) {
          Get.log("扫描到设备: ${results.length}个");
          for (var result in results) {
            if (result.advertisementData.localName != deviceName ||
                scanResultsRemoteIds
                    .contains(result.device.remoteId.toString())) {
              continue;
            } else {
              scanResultsRemoteIds.add(result.device.remoteId.toString());
              Get.log("扫描到设备名称: ${result.advertisementData.localName}");
            }
            try {
              // 连接设备以读取 SN 码
              await result.device.connect();
              List<BluetoothService> services =
                  await result.device.discoverServices();
              for (var service in services) {
                Get.log("该TOOOONY有的service: ${service.uuid}");
              }
              // 断开连接，因为我们只是读取 SN 码
              await result.device.disconnect();

              // 判断是真·TOOOONY设备
              bool hasConnectionService = services.any((service) =>
                  service.uuid.toString().toLowerCase() ==
                  deviceConnectionUUID.toLowerCase());
              // 读取SN码
              bool hasSerialService = false;
              if (hasConnectionService) {
                Get.log("有真·TOOOONY设备");
                for (var bindedDeviceSerial in bindedDevicesSerials ?? []) {
                  if (services.any((service) =>
                      service.uuid.toString().toLowerCase() ==
                      _stringToUUID(bindedDeviceSerial).toLowerCase())) {
                    scanResults.add(result);
                    hasSerialService = true;
                    break;
                  }
                }
              }

              if (hasSerialService) {
                Get.log("尝试连接真·TOOOONY设备");
                if (isConnected.value) {
                  Get.log("已连接，无需重复连接");
                  return;
                }
                // 等待1s
                await Future.delayed(const Duration(seconds: 3));
                if (isFromActivate) {
                  await connectDevice(
                    result.device,
                    deviceSerial: bindedDevicesSerials?.first,
                  );
                  Get.log("连接成功");
                } else {
                  await connectDevice(result.device);
                }
                stopScan();

                // 连接成功后执行回调
                if (onSuccess != null) {
                  Get.log("执行连接成功回调");
                  onSuccess();
                }
              }
            } catch (e) {
              Get.log("处理设备时出错: $e");
              try {
                await result.device.disconnect();
              } catch (e) {
                Get.log("断开连接时出错: $e");
              }
            }
          }
        }
      });

      // 设置超时
      Timer(const Duration(seconds: 5), () {
        if (!completer.isCompleted) {
          completer.complete(scanResults);
          stopScan();
        }
      });

      return await completer.future;
    } catch (e) {
      Get.log("扫描出错: $e");
      isScanning.value = false;
      return [];
    }
  }

  // 停止扫描
  void stopScan() {
    if (!isScanning.value) return;

    FlutterBluePlus.stopScan();
    isScanning.value = false;
  }

  // 修改连接设备方法
  Future<void> connectDevice(
    BluetoothDevice device, {
    String? deviceSerial,
  }) async {
    try {
      // 检查是否已经有连接的设备
      if (isConnected.value) {
        return;
      }

      // 开始连接
      await device.connect();
      connectedDevice.value = device;
      isConnected.value = true;
      if (deviceSerial != null) {
        connectedDeviceFromQRScan.value = device;
      }

      // 发现、订阅服务
      final success =
          await discoverServices(device, isFromActivate: deviceSerial != null);

      if (!success) {
        print("服务发现失败，断开连接");
        await disconnectDevice();
        return;
      } else {
        Get.log("服务发现成功");
        sendMessage(MessageType.chat, {"text": "连接成功"});
      }
    } catch (e) {
      print("连接设备时出错: $e");
    }
  }

  // 断开连接（只在用户主动断开时调用）
  Future<void> disconnectDevice({bool isFromActivate = false}) async {
    try {
      // 关闭所有监听
      _stopAllSubscriptions();

      // 判断当前模式
      if (isAdvertising.value) {
        // 外设模式
        if (!isConnected.value) {
          return;
        }
        await sendMessage(MessageType.system, {
          "command": "DISCONNECT",
        });
        isConnected.value = false;

        // 清除已连接的设备列表
        subscribedDevices.clear();
      } else {
        // 中心设备模式
        Get.log("中心设备模式断连执行 ---- 开始");
        if (isFromActivate) {
          await connectedDeviceFromQRScan.value!.disconnect();
        } else {
          await connectedDevice.value!.disconnect();
          isConnected.value = false;
          // 清除已发现的服务
          discoveredServices.clear();
        }
        Get.log("中心设备模式断连执行 ---- 结束");
      }
    } catch (e) {}
  }

  // 发现读写服务
  Future<bool> discoverServices(BluetoothDevice device,
      {bool isFromActivate = false}) async {
    try {
      List<BluetoothService> services = await device.discoverServices();
      Get.log("发现服务: ${services.length}个");
      discoveredServices.value = services;

      // 查找读写服务
      BluetoothService? service;
      for (var s in services) {
        if (s.uuid.toString().toUpperCase() == serviceUUIDShort) {
          service = s;
          Get.log("找到读写服务: ${service.uuid}");
          break;
        } else {
          Get.log("找到其他服务: ${s.uuid}");
        }
      }

      if (service == null) {
        Get.log("未找到读写服务");
        return false;
      }

      // 查找测试特征值
      BluetoothCharacteristic? characteristic;
      for (var char in service.characteristics) {
        if (char.uuid.toString().toUpperCase() == characteristicUUIDShort) {
          characteristic = char;
          Get.log("找到读写特征值: ${char.uuid}");
          break;
        }
      }

      if (characteristic == null) {
        Get.log("未找到读写特征值");
        return false;
      }

      //   订阅服务
      final success = await subscribeService(
          isFromActivate: isFromActivate, characteristic: characteristic);

      return success;
    } catch (e) {
      Get.log("Service discovery error: $e");
      return false;
    }
  }

  // 订阅读写服务
  Future<bool> subscribeService(
      {bool isFromActivate = false,
      required BluetoothCharacteristic characteristic}) async {
    // 如果特征值支持通知，则启用通知
    if (isFromActivate) {
      return true;
    }
    if (characteristic.properties.notify) {
      try {
        await _setNotify(characteristic, true);
        Get.log("已启用通知: ${characteristic.uuid}");
        return true;
      } catch (e) {
        Get.log("启用通知失败: ${characteristic.uuid}, 错误: $e");
        return false;
      }
    } else {
      Get.log("特征值不支持通知");
      return false;
    }
  }

  // 发送消息
  Future<void> sendMessage(MessageType type, dynamic content) async {
    Get.log("发送消息: $type, $content");
    try {
      if (!isConnected.value) {
        Get.log("未连接设备");
        Get.snackbar("提示", "请先连接设备");
        return;
      }

      // 构造消息对象
      final messageData = {
        'sender': deviceType,
        'type': type.value,
        'content': content,
        'timestamp': DateTime.now().toIso8601String(),
      };

      final message = json.encode(messageData);
      Get.log("准备发送的消息: $messageData");

      // 判断当前模式
      if (isAdvertising.value) {
        // 外设模式
        try {
          // 检查是否有连接的设备
          if (subscribedDevices.isEmpty) {
            throw Exception("没有连接的设备");
          }

          // 使用peripheral.BlePeripheral发送消息
          await peripheral.BlePeripheral.updateCharacteristic(
            characteristicId: characteristicUUID,
            value: Uint8List.fromList(utf8.encode(message)),
            deviceId: subscribedDevices.first,
          );
          Get.log("外围设备发送消息成功: $message");
        } catch (e) {
          Get.log("发送消息失败: $e");
        }
      } else {
        // 中心设备模式
        Get.log("中心设备模式发送消息");
        // 找到读写服务
        final service = discoveredServices.firstWhere(
          (service) =>
              service.uuid.toString().toUpperCase() == serviceUUIDShort,
          orElse: () {
            throw Exception("未找到测试服务");
          },
        );

        // 找到测试特征值
        final characteristic = service.characteristics.firstWhere(
          (char) =>
              char.uuid.toString().toUpperCase() == characteristicUUIDShort,
          orElse: () {
            throw Exception("未找到测试特征值");
          },
        );
        Get.log("找到读写特征值: ${characteristic.uuid}");
        // 发送消息
        await characteristic.write(Uint8List.fromList(utf8.encode(message)));
        Get.log("中心设备发送消息成功: $message");
      }
    } catch (e) {
      Get.log("发送消息失败: $e");
    }
  }

  // 消息处理函数
  void _handleMessage(String deviceType, String type, dynamic content) {
    // ble内部消息处理
    // 处理SYSTEM类型的消息
    if (type == MessageType.system.value) {
      if (content == "DISCONNECT") {
        Get.log("收到断开连接指令");
        disconnectDevice();
        return;
      }
    }
    // ble外部消息处理
    _messageHandler.handleMessage(deviceType, type, content);
  }
}
