import 'package:Toooony/components/button.dart';
import 'package:Toooony/controllers/auth.dart';
import 'package:Toooony/controllers/user.dart';
import 'package:Toooony/pages/auth/login.dart';
import 'package:Toooony/pages/profile/edit.dart';
import 'package:Toooony/pages/profile/security.dart';
import 'package:Toooony/pages/setting/setting.dart';
import 'package:Toooony/pages/profile/feedback.dart';
import 'package:Toooony/theme/theme.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class MyPage extends StatefulWidget {
  const MyPage({super.key});

  @override
  createState() => _MyWidgetState();
}

class _MyWidgetState extends State<MyPage> {
  final userController = Get.find<UserController>();

  @override
  void initState() {
    super.initState();
    userController.user.listen((value) {
      if (!mounted) return;
      setState(() {});
      if (value == null) {
        Get.to(() => const LoginPage());
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  Widget _buildSettings() {
    final theme = TonyTheme.of(context);
    return Container(
      decoration: BoxDecoration(
        color: theme.cardBackground,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          GestureDetector(
            onTap: () {
              Get.to(
                () => const AccountSecurityPage(),
                transition: Transition.rightToLeft,
              );
            },
            child: Container(
              color: Colors.transparent,
              padding: const EdgeInsets.symmetric(
                horizontal: 15,
                vertical: 13,
              ),
              child: Row(children: [
                Image.asset(
                  'assets/icons/Order.png',
                  width: 30,
                ),
                const SizedBox(
                  width: 6,
                ),
                Text(
                  'my_order'.tr,
                  style: TextStyle(
                    color: theme.onBackground,
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                const Spacer(),
                Image.asset(
                  'assets/Right.png',
                  width: 24,
                ),
              ]),
            ),
          ),
          GestureDetector(
            onTap: () {
              Get.to(
                () => const AccountSecurityPage(),
                transition: Transition.rightToLeft,
              );
            },
            child: Container(
              color: Colors.transparent,
              padding: const EdgeInsets.symmetric(
                horizontal: 15,
                vertical: 13,
              ),
              child: Row(children: [
                Image.asset(
                  'assets/icons/Account-security.png',
                  width: 30,
                ),
                const SizedBox(
                  width: 6,
                ),
                Text(
                  'account_and_security'.tr,
                  style: TextStyle(
                    color: theme.onBackground,
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                const Spacer(),
                Image.asset(
                  'assets/Right.png',
                  width: 24,
                ),
              ]),
            ),
          ),
          GestureDetector(
            onTap: () {
              Get.to(
                () => const FeedbackPage(),
                transition: Transition.rightToLeft,
              );
            },
            child: Container(
              color: Colors.transparent,
              padding: const EdgeInsets.symmetric(
                horizontal: 15,
                vertical: 13,
              ),
              child: Row(children: [
                Image.asset(
                  'assets/icons/Feedback.png',
                  width: 30,
                ),
                const SizedBox(
                  width: 6,
                ),
                Text(
                  'feedback'.tr,
                  style: TextStyle(
                    color: theme.onBackground,
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                const Spacer(),
                Image.asset(
                  'assets/Right.png',
                  width: 24,
                ),
              ]),
            ),
          ),
          GestureDetector(
            onTap: () {
              Get.to(
                () => const TonySettingsPage(),
                transition: Transition.rightToLeft,
              );
            },
            child: Container(
              color: Colors.transparent,
              padding: const EdgeInsets.symmetric(
                horizontal: 15,
                vertical: 13,
              ),
              child: Row(children: [
                Image.asset(
                  'assets/icons/Setting-two.png',
                  width: 30,
                ),
                const SizedBox(
                  width: 6,
                ),
                Text(
                  'setting'.tr,
                  style: TextStyle(
                    color: theme.onBackground,
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                const Spacer(),
                Image.asset(
                  'assets/Right.png',
                  width: 24,
                ),
              ]),
            ),
          ),
        ],
      ),
    );
  }

  Widget _userInfoCards() {
    final theme = TonyTheme.of(context);
    return Row(
      children: [
        Expanded(
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 15,
              vertical: 10,
            ),
            decoration: BoxDecoration(
              color: theme.cardBackground,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Image.asset(
                  'assets/icons/car.png',
                  width: 24,
                  height: 24,
                ),
                const SizedBox(
                  height: 5,
                ),
                Row(
                  children: [
                    Text(
                      'my_car'.tr,
                      style: TextStyle(
                        fontSize: 14,
                        color: theme.secondary,
                      ),
                    ),
                    const Spacer(),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                      color: theme.secondary,
                    )
                  ],
                )
              ],
            ),
          ),
        ),
        const SizedBox(
          width: 16,
        ),
        Expanded(
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 15,
              vertical: 10,
            ),
            decoration: BoxDecoration(
              color: theme.cardBackground,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Lv1',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontFamily: 'D-DIN-PRO',
                    fontWeight: FontWeight.w500,
                    height: 1.20,
                  ),
                ),
                const SizedBox(
                  height: 5,
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '40/100',
                      style: TextStyle(
                        color: Color(0xFF747474) /* 内容文字1 */,
                        fontSize: 12,
                        fontFamily: 'PingFang SC',
                        fontWeight: FontWeight.w400,
                        letterSpacing: -0.41,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      width: double.infinity,
                      height: 2,
                      decoration: BoxDecoration(
                        color: const Color(0xFF2A2A2A),
                        borderRadius: BorderRadius.circular(2),
                      ),
                      child: FractionallySizedBox(
                        alignment: Alignment.centerLeft,
                        widthFactor: 0.3, // 这里可以根据实际进度调整
                        child: Container(
                          decoration: BoxDecoration(
                            color: theme.priamry,
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                      ),
                    ),
                  ],
                )
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildUserInfo() {
    final theme = TonyTheme.of(context);

    final user = userController.user.value;

    if (user == null) {
      return const SizedBox.shrink();
    }

    final avatar = user.avatar.isEmpty ? 'assets/Intersect.png' : user.avatar;

    return Container(
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/gradient_bg.png'),
          fit: BoxFit.cover,
        ),
      ),
      child: Column(
        children: [
          const SizedBox(
            height: 24,
          ),
          GestureDetector(
            onTap: () {
              Get.to(() => const EditProfilePage());
            },
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                CircleAvatar(
                  radius: 40,
                  backgroundImage: avatar.startsWith('http')
                      ? NetworkImage(avatar)
                      : AssetImage(avatar),
                ),
                const SizedBox(
                  width: 10,
                ),
                Text(
                  user.nickname.isEmpty ? 'Anonymous' : user.nickname,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.w500,
                  ),
                )
              ],
            ),
          ),
          const SizedBox(
            height: 30,
          ),
          _userInfoCards(),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = TonyTheme.of(context);
    return Scaffold(
      backgroundColor: theme.background,
      body: SafeArea(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 15),
          width: Get.width,
          height: Get.height,
          child: Column(
            // crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              _buildUserInfo(),
              const SizedBox(
                height: 16,
              ),
              _buildSettings(),
              const Spacer(),
              TonyButton(
                background: TonyTheme.of(context).priamry,
                onPressed: () async {
                  final auth = Get.find<AuthController>();
                  auth.logout();
                },
                text: 'logout'.tr,
              ),
              const SizedBox(
                height: 24,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
