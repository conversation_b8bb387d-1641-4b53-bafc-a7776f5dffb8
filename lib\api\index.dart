import 'dart:io';
import 'package:Toooony/api/auth.dart';
import 'package:Toooony/api/file.dart';
import 'package:Toooony/api/watch.dart';
import 'package:Toooony/controllers/auth.dart';
import 'package:Toooony/pages/auth/login.dart';
import 'package:Toooony/utils/shared_preferences.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../utils/constants.dart';

class Api extends GetConnect {
  final authController = Get.find<AuthController>();
  final localStorage = Get.find<LocalStorage>();

  @override
  void onInit() {
    super.onInit();
    httpClient.baseUrl = API_URL;
    httpClient.errorSafety = false;
    httpClient.addRequestModifier<void>(
      (request) {
        debugPrint("Request: ${request.url}.");
        debugPrint("=== API请求token检查 ===");
        debugPrint(
            "authController.token.value: ${authController.token.value.isEmpty ? '空字符串' : '有token(${authController.token.value.substring(0, authController.token.value.length > 50 ? 50 : authController.token.value.length)}...)'}");
        debugPrint(
            "authController.token.isNotEmpty: ${authController.token.isNotEmpty}");

        if (authController.token.isNotEmpty) {
          final bearerToken = "Bearer ${authController.token.value}";
          request.headers['authorization'] = bearerToken;
          debugPrint(
              "已添加Authorization头: Bearer ${authController.token.value.substring(0, authController.token.value.length > 20 ? 20 : authController.token.value.length)}...");
        } else {
          debugPrint("警告：没有token，未添加Authorization头");
        }
        debugPrint("最终请求头: ${request.headers}");
        debugPrint("========================");
        return request;
      },
    );
    httpClient.addResponseModifier((request, response) {
      debugPrint(
          'response for ${request.url}: ${response.statusCode} ${response.body}');
      if (response.statusCode == 401) {
        debugPrint("Unauthorized request to ${request.url}");
        Get.snackbar('Auth failed!', 'please sign in again!');
        authController.token.value = '';
        localStorage.prefs.remove('token');
        Get.to(
          () => const LoginPage(),
        );
        throw Exception('401');
      } else if (response.statusCode != 200 && response.statusCode != 201) {
        throw HttpException(
            response.bodyString ?? 'HTTP error: ${response.statusCode}');
      }
      return response;
    });
  }
}

class ApiService extends GetxService {
  final api = Get.put(Api());
  late final AuthApi auth;
  late final WatchApi watch;
  late final FileApi file;

  get feedback => null;

  @override
  void onInit() {
    super.onInit();
    auth = AuthApi(api);
    watch = WatchApi(api);
    file = FileApi(api);
  }
}
