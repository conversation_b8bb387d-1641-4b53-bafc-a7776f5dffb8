import 'package:flutter/material.dart';
import 'package:get/get.dart';

void showTonyDialog(BuildContext context, Widget dialog) {
  Get.dialog(dialog, barrierColor: Colors.transparent);
}

Future<bool> showConfirmDialog(BuildContext context,
    {String? title, String? content}) async {
  return await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(title ?? "删除确认"),
          content: Text(content ?? "确定要删除这条数据吗？此操作不可撤销！"),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false), // 返回false
              child: const Text("取消"),
            ),
            TextButton(
                onPressed: () {
                Navigator.pop(context, true); // 返回true
              },
              child: const Text("确认", style: TextStyle(color: Colors.red)),
            ),
          ],
        ),
      ) ??
      false; // 如果点击外部关闭弹窗，返回false
}
