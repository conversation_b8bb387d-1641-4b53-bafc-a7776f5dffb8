import 'package:Toooony/components/app_bar.dart';
import 'package:Toooony/components/switch.dart';
import 'package:Toooony/components/watch/face.dart';
import 'package:Toooony/controllers/watch.dart';
import 'package:Toooony/theme/theme.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class BrightnessSettingPage extends StatefulWidget {
  const BrightnessSettingPage({super.key});

  @override
  State<BrightnessSettingPage> createState() => _BrightnessSettingPageState();
}

const brightnessList = [
  20,
  50,
  100,
];

class _BrightnessSettingPageState extends State<BrightnessSettingPage> {
  int selectedBrightness = 2; // 0: 低亮, 1: 中亮, 2: 高亮

  final watchController = Get.find<WatchController>();

  void _selectBrightness(int brightness) {
    setState(() {
      selectedBrightness = brightness;
    });
    watchController.switchBrightness(brightnessList[brightness]);
  }

  @override
  Widget build(BuildContext context) {
    final theme = TonyTheme.of(context);
    return Scaffold(
      backgroundColor: theme.background,
      body: SafeArea(
        child: Container(
          height: Get.height,
          width: Get.width,
          padding: const EdgeInsets.symmetric(
            horizontal: 15,
          ),
          // decoration: const BoxDecoration(
          //   image: DecorationImage(
          //     image: AssetImage(
          //       'assets/home_bg.png',
          //     ),
          //     fit: BoxFit.cover,
          //   ),
          // ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const TonyAppBar(
                middle: '亮度设置',
              ),
              const SizedBox(
                height: 20,
              ),
              const SizedBox(
                height: 48,
              ),
              const TonyFace(
                width: 240,
              ),
              const SizedBox(
                height: 48,
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                decoration: BoxDecoration(
                  color: theme.cardBackground,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    GestureDetector(
                      onTap: () => _selectBrightness(0),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          vertical: 15,
                        ),
                        decoration: BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: theme.background,
                              width: 1.5,
                            ),
                          ),
                        ),
                        child: Row(
                          children: [
                            Text(
                              '低亮',
                              style: TextStyle(
                                color: theme.onBackground,
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                            const Spacer(),
                            if (selectedBrightness == 0)
                              Image.asset(
                                'assets/Check-small.png',
                                width: 24,
                              ),
                          ],
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () => _selectBrightness(1),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          vertical: 15,
                        ),
                        decoration: BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: theme.background,
                              width: 1.5,
                            ),
                          ),
                        ),
                        child: Row(
                          children: [
                            Text(
                              '中亮',
                              style: TextStyle(
                                color: theme.onBackground,
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                            const Spacer(),
                            if (selectedBrightness == 1)
                              Image.asset(
                                'assets/Check-small.png',
                                width: 24,
                              ),
                          ],
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () => _selectBrightness(2),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          vertical: 15,
                        ),
                        decoration: BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: theme.background,
                              width: 1.5,
                            ),
                          ),
                        ),
                        child: Row(
                          children: [
                            Text(
                              '高亮',
                              style: TextStyle(
                                color: theme.onBackground,
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                            const Spacer(),
                            if (selectedBrightness == 2)
                              Image.asset(
                                'assets/Check-small.png',
                                width: 24,
                              ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                decoration: BoxDecoration(
                  color: theme.cardBackground,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        vertical: 15,
                      ),
                      child: Row(
                        children: [
                          Text(
                            '夜间',
                            style: TextStyle(
                              color: theme.onBackground,
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                          const Spacer(),
                          const TonySwitch(),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const Spacer(),
            ],
          ),
        ),
      ),
    );
  }
}
