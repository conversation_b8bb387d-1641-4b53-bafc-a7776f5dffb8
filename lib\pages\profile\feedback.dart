import 'package:Toooony/api/index.dart';
import 'package:Toooony/components/app_bar.dart';
import 'package:Toooony/components/button.dart';
import 'package:Toooony/controllers/user.dart';
import 'package:Toooony/theme/theme.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class FeedbackPage extends StatefulWidget {
  const FeedbackPage({super.key});

  @override
  State<FeedbackPage> createState() => _FeedbackPageState();
}

class _FeedbackPageState extends State<FeedbackPage>
    with TickerProviderStateMixin {
  final TextEditingController _feedbackController = TextEditingController();
  final TextEditingController _contactController = TextEditingController();
  final userController = Get.find<UserController>();

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  String _selectedType = 'bug_report';
  final Map<String, Map<String, dynamic>> _feedbackTypes = {
    'bug_report': {
      'title': '问题反馈',
      'subtitle': '遇到了Bug或功能异常',
      'icon': Icons.bug_report,
      'color': Colors.red,
    },
    'feature_request': {
      'title': '功能建议',
      'subtitle': '希望增加新功能',
      'icon': Icons.lightbulb_outline,
      'color': Colors.orange,
    },
    'user_experience': {
      'title': '体验优化',
      'subtitle': '界面或交互体验问题',
      'icon': Icons.favorite_outline,
      'color': Colors.blue,
    },
    'other': {
      'title': '其他反馈',
      'subtitle': '其他意见或建议',
      'icon': Icons.chat_bubble_outline,
      'color': Colors.green,
    },
  };

  bool _isSubmitting = false;
  int _characterCount = 0;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();

    _feedbackController.addListener(() {
      setState(() {
        _characterCount = _feedbackController.text.length;
      });
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _feedbackController.dispose();
    _contactController.dispose();
    super.dispose();
  }

  Widget _buildUserInfo() {
    final theme = TonyTheme.of(context);
    final user = userController.user.value;

    if (user == null) return const SizedBox.shrink();

    final avatar = user.avatar.isEmpty ? 'assets/Intersect.png' : user.avatar;

    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
        color: theme.cardBackground,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 25,
            backgroundImage: avatar.startsWith('http')
                ? NetworkImage(avatar)
                : AssetImage(avatar) as ImageProvider,
          ),
          const SizedBox(width: 15),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user.nickname.isEmpty ? '匿名用户' : user.nickname,
                  style: TextStyle(
                    color: theme.onBackground,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '感谢您的宝贵意见',
                  style: TextStyle(
                    color: theme.secondary,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.feedback_outlined,
            color: theme.priamry,
            size: 24,
          ),
        ],
      ),
    );
  }

  Widget _buildTypeSelector() {
    final theme = TonyTheme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '反馈类型',
          style: TextStyle(
            color: theme.onBackground,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          decoration: BoxDecoration(
            color: theme.cardBackground,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: _feedbackTypes.entries.map((entry) {
              final key = entry.key;
              final value = entry.value;
              final isSelected = _selectedType == key;
              final isLast = key == _feedbackTypes.keys.last;

              return AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                decoration: BoxDecoration(
                  color: isSelected
                      ? theme.priamry.withOpacity(0.1)
                      : Colors.transparent,
                  borderRadius: BorderRadius.vertical(
                    top: key == _feedbackTypes.keys.first
                        ? const Radius.circular(12)
                        : Radius.zero,
                    bottom: isLast ? const Radius.circular(12) : Radius.zero,
                  ),
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () {
                      setState(() {
                        _selectedType = key;
                      });
                    },
                    borderRadius: BorderRadius.vertical(
                      top: key == _feedbackTypes.keys.first
                          ? const Radius.circular(12)
                          : Radius.zero,
                      bottom: isLast ? const Radius.circular(12) : Radius.zero,
                    ),
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: isLast
                            ? null
                            : Border(
                                bottom: BorderSide(
                                  color: theme.secondary.withOpacity(0.1),
                                  width: 0.5,
                                ),
                              ),
                      ),
                      child: Row(
                        children: [
                          AnimatedContainer(
                            duration: const Duration(milliseconds: 200),
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? (value['color'] as Color).withOpacity(0.2)
                                  : theme.secondary.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              value['icon'] as IconData,
                              color: isSelected
                                  ? value['color'] as Color
                                  : theme.secondary,
                              size: 20,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  value['title'] as String,
                                  style: TextStyle(
                                    color: theme.onBackground,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                Text(
                                  value['subtitle'] as String,
                                  style: TextStyle(
                                    color: theme.secondary,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          AnimatedScale(
                            scale: isSelected ? 1.0 : 0.0,
                            duration: const Duration(milliseconds: 200),
                            child: Icon(
                              Icons.check_circle,
                              color: theme.priamry,
                              size: 20,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildContentInput() {
    final theme = TonyTheme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '详细描述',
          style: TextStyle(
            color: theme.onBackground,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          '请详细描述您遇到的问题或建议',
          style: TextStyle(
            color: theme.secondary,
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          decoration: BoxDecoration(
            color: theme.cardBackground,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              TextField(
                controller: _feedbackController,
                maxLines: 6,
                maxLength: 500,
                decoration: InputDecoration(
                  hintText: '请详细描述您的问题或建议...',
                  hintStyle: TextStyle(
                    color: theme.secondary,
                    fontSize: 14,
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.all(16),
                  counterText: '',
                ),
                style: TextStyle(
                  color: theme.onBackground,
                  fontSize: 14,
                  height: 1.5,
                ),
              ),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  border: Border(
                    top: BorderSide(
                      color: theme.secondary.withOpacity(0.1),
                      width: 0.5,
                    ),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '字数统计',
                      style: TextStyle(
                        color: theme.secondary,
                        fontSize: 12,
                      ),
                    ),
                    Text(
                      '$_characterCount/500',
                      style: TextStyle(
                        color: _characterCount > 450
                            ? Colors.red
                            : theme.secondary,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildContactInput() {
    final theme = TonyTheme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '联系方式',
          style: TextStyle(
            color: theme.onBackground,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          '选填，便于我们及时回复您（手机号/邮箱）',
          style: TextStyle(
            color: theme.secondary,
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          decoration: BoxDecoration(
            color: theme.cardBackground,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TextField(
            controller: _contactController,
            decoration: InputDecoration(
              hintText: '请输入手机号或邮箱地址',
              hintStyle: TextStyle(
                color: theme.secondary,
                fontSize: 14,
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.all(16),
              prefixIcon: Icon(
                Icons.contact_mail_outlined,
                color: theme.secondary,
                size: 20,
              ),
            ),
            style: TextStyle(
              color: theme.onBackground,
              fontSize: 14,
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = TonyTheme.of(context);

    return Scaffold(
      backgroundColor: theme.background,
      body: SafeArea(
        child: Column(
          children: [
            const TonyAppBar(middle: '意见反馈'),
            Expanded(
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildUserInfo(),
                      _buildTypeSelector(),
                      const SizedBox(height: 24),
                      _buildContentInput(),
                      const SizedBox(height: 24),
                      _buildContactInput(),
                      const SizedBox(height: 40),
                    ],
                  ),
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.background,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: TonyButton(
                onPressed: () {
                  if (!_isSubmitting) {
                    _handleSubmit();
                  }
                },
                text: _isSubmitting ? '提交中...' : '提交反馈',
                background: _isSubmitting ? theme.secondary : theme.priamry,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleSubmit() {
    if (_feedbackController.text.trim().isEmpty) {
      Get.snackbar(
        '提示',
        '请填写反馈内容',
        backgroundColor: TonyTheme.of(context).modalBackground,
        colorText: Colors.white,
        icon: const Icon(Icons.warning, color: Colors.white),
      );
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    _submitFeedback();
  }

  Future<void> _submitFeedback() async {
    try {
      // TODO: Implement actual feedback submission API call
      // Example:
      // await Get.find<ApiService>().submitFeedback(
      //   type: _selectedType,
      //   content: _feedbackController.text.trim(),
      //   contact: _contactController.text.trim(),
      // );

      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      Get.back();
      Get.snackbar(
        '成功',
        '反馈提交成功，感谢您的宝贵意见！',
        backgroundColor: TonyTheme.of(context).priamry,
        colorText: Colors.white,
        icon: const Icon(Icons.check_circle, color: Colors.white),
      );
    } catch (e) {
      Get.snackbar(
        '错误',
        '提交失败，请重试',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        icon: const Icon(Icons.error, color: Colors.white),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }
}
