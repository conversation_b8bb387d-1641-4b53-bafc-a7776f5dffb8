import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
// 异步获取
//shared_preferences 是一个Flutter插件，用于读取和写入键值对数据到持久化存储。
// 这通常用于存储简单的数据，比如用户设置或最近的搜索查询。
class LocalStorage extends GetxService {
  late final SharedPreferences prefs;

  Future<LocalStorage> init() async {
    prefs = await SharedPreferences.getInstance();
    debugPrint('finish initing local storage');
    // prefs.clear();
    return this;
  }
}
