import 'dart:convert';

import 'package:Toooony/api/index.dart';
import 'package:Toooony/components/dialogs/loading.dart';
import 'package:Toooony/components/watch/edit.dart';
import 'package:Toooony/components/watch/face.dart';
import 'package:Toooony/components/watch/url_cover.dart';
import 'package:Toooony/controllers/bluetooth.dart';
import 'package:Toooony/controllers/watch.dart';
import 'package:Toooony/model/watch.dart';
import 'package:Toooony/pages/setting/hotspot.dart';
import 'package:Toooony/pages/setting/setting.dart';
import 'package:Toooony/pages/setting/watch_setting.dart';
import 'package:Toooony/pages/watch/all_devices.dart';
import 'package:Toooony/pages/scan/scan.dart';
import 'package:Toooony/theme/theme.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _HomePageState();
  }
}

final dashTabs = [
  DashType.Official,
  DashType.AI,
  DashType.ImageBackground,
  DashType.Dash3D,
  DashType.URL,
];

class _HomePageState extends State<HomePage>
    with SingleTickerProviderStateMixin {
  final WatchController watchController = Get.find();

  final refreshController = RefreshController();
  late PageController pageController; // 添加 PageController

  late TabController tabController;

  final bleController = Get.find<BLEController>();

  final tabs = [
    Tab(text: 'official'.tr),
    Tab(text: 'ai'.tr),
    Tab(text: 'photo_background'.tr),
    Tab(text: '3D'.tr),
    Tab(text: 'URL'.tr),
  ];

  List<WatchInstance> currentInstances = [];

  @override
  void initState() {
    super.initState();

    pageController = PageController(); // 初始化 PageController

    tabController = TabController(length: tabs.length, vsync: this)
      ..addListener(() {
        if (mounted) {
          setState(() {});
        }
      });

    // 监听设备变化，更新 PageView 当前页
    watchController.device.listen((currentDevice) {
      if (mounted && currentDevice != null) {
        final devices = watchController.devices;
        final currentIndex =
            devices.indexWhere((device) => device.id == currentDevice.id);
        if (currentIndex != -1 && pageController.hasClients) {
          pageController.animateToPage(
            currentIndex,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
      }
    });

    watchController.currentTabIndex.listen((index) {
      if (mounted && tabController.index != index) {
        tabController.animateTo(index);
      }
    });

    watchController.currentWatchID.addListener(() {
      if (mounted) {
        setState(() {});
      }
    });

    watchController.instances.addListener(() {
      if (mounted) {
        Get.log('instances changed');
        setState(() {});
      }
    });

    initBleState();
  }

  Future<void> initBleState() async {
    var status = await Permission.bluetooth.status;
    if (status.isGranted) {
      Get.log("蓝牙权限已授予");
      // final bleController = Get.find<BLEController>();

      // 要通过watch里面的devices来获取已绑定的设备，然后传serial过来
      // final scanResults = await bleController.startScan(
      //   bindedDevicesSerials: ['a177d68b635f89f1'],
      // );
      // Get.log("scanResults: $scanResults");
    }
  }

  // 处理扫描结果
  Future<void> handleQRScanResult(dynamic result) async {
    // Get.log("handleQRScanResult: $result");
    // if (result is! ScanResult) {
    //   Get.log("无效的扫描结果类型");
    //   return;
    // }

    // // // 显示确认弹窗
    // // final bool? confirm = await showDialog<bool>(
    // //   context: context,
    // //   builder: (BuildContext context) => AlertDialog(
    // //     title: const Text('确认激活'),
    // //     content: const Text('是否确认激活此设备？'),
    // //     actions: [
    // //       TextButton(
    // //         onPressed: () => Navigator.of(context).pop(false),
    // //         child: const Text('取消'),
    // //       ),
    // //       TextButton(
    // //         onPressed: () => Navigator.of(context).pop(true),
    // //         child: const Text('确认'),
    // //       ),
    // //     ],
    // //   ),
    // // );

    // // if (confirm != true) {
    // //   return;
    // // }

    // try {
    //   final api = Get.find<ApiService>();

    //   Get.log(
    //     "请求体: ${jsonEncode({'serial': result.serial, 'encStr': result.encStr, 'time': int.parse(result.time)})}",
    //   );
    //   发送HTTP请求
    //   final res = await api.auth.bindDevice(
    //     result.serial,
    //     result.encStr,
    //     int.parse(result.time),
    //   );

    //   Get.log("响应内容: ${res.body}");

    //   if (res.statusCode == 200) {
    //     Get.log("绑定成功，返回内容: ${res.body}");
    //     // 解析返回的token
    //     final responseData = jsonDecode(res.body);
    //     final token = responseData['token'] as String?;
    //     if (token != null) {
    //       Get.log("获取到的token: $token");
    //     } else {
    //       Get.log("未找到token");
    //     }
    //     Get.snackbar('成功', '设备绑定成功');

    //     // 开始扫描并连接设备
    //     bleController.stopScan();
    //     await bleController.startScan(
    //       isFromActivate: true,
    //       bindedDevicesSerials: [result.serial],
    //     );
    //   } else {
    //     Get.log("绑定失败，状态码: ${res.statusCode}");
    //     Get.snackbar('错误', '绑定失败: ${res.body}');
    //   }
    // } catch (e) {
    //   Get.log("绑定请求出错: $e");
    //   Get.snackbar('错误', '绑定请求失败');
    // }
  }

  @override
  void dispose() {
    pageController.dispose(); // 释放 PageController
    tabController.dispose();
    super.dispose();
  }

  Widget unbindView(BuildContext context) {
    final theme = TonyTheme.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: Column(
        children: [
          const SizedBox(height: 42),
          topView(context),
          SizedBox(
            height: 480,
            child: Stack(
              children: [
                Positioned(
                  bottom: 0,
                  left: 25,
                  child: Container(
                    width: Get.width - 80, // w - 15*2 - 25*2
                    padding: const EdgeInsets.symmetric(
                      horizontal: 17,
                      vertical: 40,
                    ),
                    decoration: ShapeDecoration(
                      color: const Color(0xFF202020),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          '您还未绑定任何Tooooony',
                          style: TextStyle(
                            color: theme.secondary,
                            fontSize: 15,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            '如何绑定？',
                            style: TextStyle(
                              color: theme.secondary,
                              fontSize: 13,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            '1、将Toooony接入电源会自动开机',
                            style: TextStyle(
                              color: theme.secondary,
                              fontSize: 13,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            '2、未绑定的Toooony开机后屏幕会显示二维码',
                            style: TextStyle(
                              color: theme.secondary,
                              fontSize: 13,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            '3、扫描Toooony屏幕上的二维码完成绑定',
                            style: TextStyle(
                              color: theme.secondary,
                              fontSize: 13,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                        const SizedBox(height: 26),
                        GestureDetector(
                          onTap: () async {
                            final result = await Get.to(
                              () => const ScanQRPage(),
                              transition: Transition.rightToLeft,
                            );

                            // 如果扫描页面返回true，说明绑定成功，需要刷新设备信息
                            if (result == true) {
                              Get.log('[主页] 检测到设备绑定成功，开始刷新设备信息');
                              try {
                                await watchController.refreshAfterBinding();
                                Get.log('[主页] 设备信息刷新完成');
                              } catch (e) {
                                Get.log('[主页] 刷新设备信息失败: $e');
                              }
                            }
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 28,
                              vertical: 10,
                            ),
                            decoration: ShapeDecoration(
                              color: const Color(0xFF202020),
                              shape: RoundedRectangleBorder(
                                side: const BorderSide(
                                  width: 1,
                                  color: Color(0xFF4D4D4D),
                                ),
                                borderRadius: BorderRadius.circular(20),
                              ),
                            ),
                            child: Text(
                              '扫码绑定',
                              style: TextStyle(
                                color: theme.reversedPrimary,
                                fontSize: 15,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Positioned(
                  left: (Get.width - 136 - 30) / 2,
                  bottom: 272,
                  child: Image.asset('assets/qr.png', width: 132),
                ),
              ],
            ),
          ),
          const SizedBox(height: 40),
          // Text(
          //   '点击查看视频教程',
          //   style: TextStyle(
          //     color: theme.secondary,
          //     fontSize: 15,
          //     fontFamily: 'PingFang SC',
          //     fontWeight: FontWeight.w400,
          //     decoration: TextDecoration.underline,
          //     decorationColor: theme.secondary,
          //     decorationThickness: 2,
          //   ),
          // ),
        ],
      ),
    );
  }

//默认表盘的显示
  Widget tonyFace() {
    final currentInstance = watchController.currentInstance();
    return Align(
      alignment: Alignment.center,
      child: TonyFace(
        width: Get.width * 236 / 375,
        image: currentInstance == null
            ? 'assets/default_watch.png'
            : currentInstance.cover,
      ),
    );
  }

//AI表盘的显示
  Widget tonyCover(BuildContext context, WatchInstance? instance) {
    if (instance == null) {
      return Container(
        margin: const EdgeInsets.only(right: 12),
        width: 100,
        height: 100,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(999),
          child: Image.asset(
            'assets/watch1.png',
            width: 100,
            height: 100,
            fit: BoxFit.cover,
          ),
        ),
      );
    }

    if (instance.type == DashType.URL) {
      return URLCover(instance: instance, key: Key(instance.id.toString()));
    }

    return Container(
      margin: const EdgeInsets.only(right: 12),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(999),
        child: CachedNetworkImage(
          imageUrl: instance.cover,
          width: 100,
          height: 100,
          fit: BoxFit.cover,
        ),
      ),
    );
  }

  //头部区域
  Widget topView(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: Row(
        children: [
          Image.asset('assets/logo.png', width: 132),
          const Spacer(),
          GestureDetector(
            onTap: () {
              Get.to(
                () => const AllDevicesPage(),
                transition: Transition.rightToLeft,
              );
            },
            child: Image.asset(
              'assets/all-application.png',
              width: 24,
            ),
          ),
          const SizedBox(width: 20),
          GestureDetector(
            onTap: () async {
              final result = await Get.to(
                () => const ScanQRPage(),
                transition: Transition.rightToLeft,
              );

              // 如果扫描页面返回true，说明绑定成功，需要刷新设备信息
              if (result == true) {
                Get.log('[主页] 检测到设备绑定成功，开始刷新设备信息');
                try {
                  await watchController.refreshAfterBinding();
                  Get.log('[主页] 设备信息刷新完成');
                } catch (e) {
                  Get.log('[主页] 刷新设备信息失败: $e');
                }
              }
            },
            child: Image.asset('assets/scan.png', width: 24),
          ),
        ],
      ),
    );
  }

  Widget deviceCard(BuildContext context, Device device, bool isCurrentDevice) {
    final theme = TonyTheme.of(context);
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10),
      decoration: BoxDecoration(
        color: theme.cardBackground,
        borderRadius: BorderRadius.circular(12),
        border:
            isCurrentDevice ? Border.all(color: theme.priamry, width: 2) : null,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: GestureDetector(
        onTap: () async {
          if (!isCurrentDevice) {
            showLoadingDialog();
            try {
              await watchController.switchToDevice(device);
            } catch (e) {
              Get.log('切换设备失败: $e');
            } finally {
              Get.closeAllDialogs();
            }
          }
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Align(
              alignment: Alignment.centerRight,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 8,
                  vertical: 2,
                ),
                decoration: BoxDecoration(
                  color: theme.reversedPrimary,
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(8),
                    bottomLeft: Radius.circular(8),
                  ),
                ),
                child: const Text(
                  '在线',
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w400,
                    color: Colors.black,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 10),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: Text(
                device.serial,
                style: TextStyle(
                  fontSize: 12,
                  color: theme.onBackground,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(height: 15),
            Image.asset(
              'assets/Face.png',
              width: 60,
            ),
            const SizedBox(height: 15),
            if (isCurrentDevice)
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(999),
                  color: theme.priamry,
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 4,
                ),
                child: const Text(
                  '当前设备',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              )
            else
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(999),
                  border: Border.all(
                    color: theme.borderColor,
                  ),
                  color: Colors.transparent,
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 4,
                ),
                child: Text(
                  '切换',
                  style: TextStyle(
                    color: theme.secondary,
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget deck(BuildContext context) {
    return Container(
      width: Get.width,
      height: Get.width * 329 / 375,
      decoration: const BoxDecoration(
        color: Colors.transparent,
        image: DecorationImage(
          image: AssetImage('assets/home_bg.png'),
          fit: BoxFit.contain,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          const SizedBox(height: 36),
          topView(context),
          const Spacer(),
          // 表盘卡片滑动区域
          Obx(() {
            final devices = watchController.devices;
            final currentDevice = watchController.device.value;

            if (devices.isEmpty) {
              return TonyFace(
                width: Get.width * 236 / 375,
                image: 'assets/default_watch.png',
              );
            }

            return SizedBox(
              height: Get.width * 236 / 375,
              child: PageView.builder(
                controller: pageController, // 使用类中定义的 pageController
                itemCount: devices.length,
                // 移除 onPageChanged 中的设备切换逻辑
                itemBuilder: (context, index) {
                  final device = devices[index];
                  final isCurrentDevice = currentDevice?.id == device.id;
                  final currentInstance = isCurrentDevice
                      ? watchController.currentInstance()
                      : null;

                  return GestureDetector(
                    onTap: () async {
                      if (!isCurrentDevice) {
                        showLoadingDialog();
                        try {
                          await watchController.switchToDevice(device);
                        } catch (e) {
                          Get.log('切换设备失败: $e');
                        } finally {
                          Get.closeAllDialogs();
                        }
                      }
                    },
                    child: Center(
                      child: TonyFace(
                        width: Get.width * 236 / 375,
                        image: currentInstance == null
                            ? 'assets/default_watch.png'
                            : currentInstance.cover,
                      ),
                    ),
                  );
                },
              ),
            );
          }),
          const SizedBox(height: 0),
        ],
      ),
    );
  }

  Widget home(BuildContext context) {
    final theme = TonyTheme.of(context);

    return Container(
      width: Get.width,
      height: Get.height,
      color: theme.background,
      child: Column(
        children: [
          deck(context),

          // 指示器区域 - 在 deck 和 TabBar 之间
          Obx(() {
            final devices = watchController.devices;
            final currentDevice = watchController.device.value;

            if (devices.length > 1) {
              return Padding(
                padding: const EdgeInsets.only(top: 15, bottom: 5),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    devices.length,
                    (index) => Container(
                      margin: const EdgeInsets.symmetric(horizontal: 3),
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: currentDevice?.id == devices[index].id
                            ? theme.priamry
                            : Colors.grey.withOpacity(0.5),
                      ),
                    ),
                  ),
                ),
              );
            }
            return const SizedBox(height: 15);
          }),

          // TabBar 区域
          Row(
            children: [
              Expanded(
                child: TabBar(
                  tabs: tabs,
                  controller: tabController,
                  indicatorSize: TabBarIndicatorSize.label,
                  indicator: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(width: 2, color: theme.priamry),
                    ),
                  ),
                  labelStyle: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                  unselectedLabelStyle: const TextStyle(
                    color: Color(0xFF747474),
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                  ),
                  tabAlignment: TabAlignment.start,
                  isScrollable: true,
                  indicatorPadding: const EdgeInsets.only(bottom: 4),
                  labelPadding: const EdgeInsets.symmetric(horizontal: 16),
                  dividerColor: Colors.transparent,
                ),
              ),
              Row(
                children: [
                  GestureDetector(
                    onTap: () {
                      Get.bottomSheet(
                        const EditWatchModal(),
                        persistent: false,
                        isScrollControlled: true,
                      );
                    },
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: Image.asset('assets/icons/Edit.png', width: 24),
                    ),
                  ),
                ],
              ),
            ],
          ),

          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 15),
              child: SmartRefresher(
                controller: refreshController,
                onRefresh: () async {
                  try {
                    await watchController.refreshInstances();
                    await watchController.refreshAllDevices();
                    refreshController.refreshCompleted();
                  } catch (e) {
                    e.printError();
                    refreshController.refreshFailed();
                  }
                },
                child: ListView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  children: [
                    const SizedBox(height: 24),

                    // 表盘列表
                    SizedBox(
                      height: 100,
                      child: Obx(
                        () => ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount: watchController
                              .instancesMap[dashTabs[tabController.index]]!
                              .length,
                          itemBuilder: (context, index) => GestureDetector(
                            onTap: () {
                              watchController.switchDashboard(
                                watchController
                                    .instancesMap[
                                        dashTabs[tabController.index]]![index]
                                    .id,
                              );
                            },
                            child: watchController
                                        .instancesMap[dashTabs[
                                            tabController.index]]![index]
                                        .id ==
                                    -1
                                ? tonyCover(context, null)
                                : tonyCover(
                                    context,
                                    watchController.instancesMap[
                                        dashTabs[tabController.index]]![index],
                                  ),
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // 设置区域
                    Container(
                      decoration: BoxDecoration(
                        color: theme.cardBackground,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Column(
                        children: [
                          GestureDetector(
                            onTap: () {
                              Get.to(
                                () => const HotspotSettingPage(),
                                transition: Transition.rightToLeft,
                              );
                            },
                            child: Container(
                              color: Colors.transparent,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 15,
                                vertical: 13,
                              ),
                              child: Row(
                                children: [
                                  Image.asset('assets/Setting1.png', width: 30),
                                  const SizedBox(width: 6),
                                  Text(
                                    'hotspot_settings'.tr,
                                    style: TextStyle(
                                      color: theme.onBackground,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const Spacer(),
                                  Image.asset('assets/Right.png', width: 24),
                                ],
                              ),
                            ),
                          ),
                          GestureDetector(
                            onTap: () {
                              Get.to(
                                () => const WatchSettingsPage(),
                                transition: Transition.rightToLeft,
                              );
                            },
                            child: Container(
                              color: Colors.transparent,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 15,
                                vertical: 13,
                              ),
                              child: Row(
                                children: [
                                  Image.asset('assets/Setting2.png', width: 30),
                                  const SizedBox(width: 6),
                                  Text(
                                    'toony_settings'.tr,
                                    style: TextStyle(
                                      color: theme.onBackground,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const Spacer(),
                                  Image.asset('assets/Right.png', width: 24),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = TonyTheme.of(context);
    return Scaffold(
      backgroundColor: theme.background,
      body: SafeArea(
        top: false,
        // child: unbindView(context),
        child: Obx(
          () => watchController.device.value == null
              ? unbindView(context)
              : home(context),
        ),
      ),
    );
  }
}
