import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

abstract class Labeled {
  String getLabel();
}

Future<T?> showSelector<T extends Labeled>(
    BuildContext context, List<T> options,
    {Widget Function(T)? render}) async {
  return showModalBottomSheet<T?>(
      context: context,
      builder: (context) {
        return Picker<T>(
          options: options,
          render: render,
        );
      });
}

class Picker<T extends Labeled> extends StatefulWidget {
  final List<T> options;
  final Widget Function(T)? render;

  const Picker({super.key, required this.options, this.render});

  @override
  State<StatefulWidget> createState() {
    return _PickerState();
  }
}

class _PickerState extends State<Picker> {
  int selectedIndex = 0;

  void onSelectedItemChanged(int index) {
    setState(() {
      selectedIndex = index;
    });
  }

  Widget itemBuilder(BuildContext context, int index) {
    final option = widget.options[index];
    return Center(
      child: widget.render != null
          ? widget.render!(option)
          : Text(option.getLabel()),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 20),
      height: Get.height * 0.4,
      child: Column(
        children: [
          GestureDetector(
            onTap: () {
              Navigator.of(context).pop(widget.options[selectedIndex]);
            },
            child: Container(
              alignment: Alignment.centerRight,
              child: const Text('完成'),
            ),
          ),
          Expanded(
              child: CupertinoPicker.builder(
                  itemExtent: 48,
                  childCount: widget.options.length,
                  onSelectedItemChanged: onSelectedItemChanged,
                  itemBuilder: itemBuilder))
        ],
      ),
    );
  }
}
