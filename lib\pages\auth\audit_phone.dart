import 'package:Toooony/theme/theme.dart';
import 'package:flutter/material.dart';

class AuditPhonePage extends StatefulWidget {
  const AuditPhonePage({super.key});

  @override
  State<AuditPhonePage> createState() => _AuditState();
}

class _AuditState extends State<AuditPhonePage> {
  @override
  Widget build(BuildContext context) {
    final theme = TonyTheme.of(context);
    return Scaffold(
      backgroundColor: theme.background,
      body: <PERSON><PERSON><PERSON>(
        child: <PERSON>um<PERSON>(
          children: [
            Text(
              '更换手机号',
              style: TextStyle(
                fontSize: 24,
                color: theme.onBackground,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
