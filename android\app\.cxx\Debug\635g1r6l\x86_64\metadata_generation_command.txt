                        -HC:\dev\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=C:\dev\SDK\ndk\26.3.11579264
-DCMAKE_ANDROID_NDK=C:\dev\SDK\ndk\26.3.11579264
-DCMAKE_TOOLCHAIN_FILE=C:\dev\SDK\ndk\26.3.11579264\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\dev\SDK\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\Users\<USER>\Desktop\company\toooony\tony-flutter\build\app\intermediates\cxx\Debug\635g1r6l\obj\x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\Users\<USER>\Desktop\company\toooony\tony-flutter\build\app\intermediates\cxx\Debug\635g1r6l\obj\x86_64
-DCMAKE_BUILD_TYPE=Debug
-BC:\Users\<USER>\Desktop\company\toooony\tony-flutter\android\app\.cxx\Debug\635g1r6l\x86_64
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2