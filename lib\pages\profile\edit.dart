import 'dart:io';

import 'package:Toooony/api/index.dart';
import 'package:Toooony/components/button.dart';
import 'package:Toooony/components/picker.dart';
import 'package:Toooony/controllers/user.dart';
import 'package:Toooony/model/auth.dart';
import 'package:Toooony/theme/theme.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class EditProfilePage extends StatefulWidget {
  const EditProfilePage({super.key});

  @override
  State<EditProfilePage> createState() => _EditProfilePageState();
}

class _EditProfilePageState extends State<EditProfilePage> {
  final nameController = TextEditingController();
  final whatsupController = TextEditingController();

  final userController = Get.find<UserController>();

  Gender gender = Gender.MALE;

  File? avatarFile;

  late User user;

  @override
  void initState() {
    super.initState();

    user = userController.user.value!;

    nameController.text = user.nickname;
    whatsupController.text = user.whatsup;

    whatsupController.addListener(() {
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = TonyTheme.of(context);

    final avatar = user.avatar.isEmpty ? 'assets/Intersect.png' : user.avatar;

    return Scaffold(
      backgroundColor: theme.background,
      body: SafeArea(
        bottom: false,
        child: Container(
          width: Get.width,
          padding: const EdgeInsets.symmetric(horizontal: 30),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 20,
              ),
              Image.asset(
                'assets/logo.png',
                width: 130,
              ),
              const SizedBox(
                height: 20,
              ),
              Text(
                '完善个人信息',
                style: TextStyle(
                  color: TonyTheme.of(context).onBackground,
                  fontSize: 24,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(
                height: 5,
              ),
              const Text(
                '只有完善个信息后才可使用拍一拍功能',
                style: TextStyle(
                  color: Color(0xFF4D4D4D),
                  fontSize: 16,
                  fontFamily: 'PingFang SC',
                  fontWeight: FontWeight.w400,
                  letterSpacing: -0.30,
                ),
              ),
              const SizedBox(
                height: 30,
              ),
              Align(
                alignment: Alignment.center,
                child: GestureDetector(
                  onTap: () {
                    showImagePicker<String>(theme, ImagePickerSheet(
                      onSelect: (value) {
                        Get.log(value.path);
                        avatarFile = File(value.path);
                        Get.closeAllBottomSheets();
                        setState(() {});
                      },
                    ));
                  },
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      CircleAvatar(
                        radius: 40,
                        backgroundImage: avatarFile != null
                            ? FileImage(avatarFile!)
                            : avatar.startsWith('http')
                                ? NetworkImage(avatar)
                                : AssetImage(avatar),
                      ),
                      const SizedBox(
                        height: 5,
                      ),
                      Text(
                        '点击设置头像',
                        style: TextStyle(
                          color: theme.secondary,
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(
                height: 20,
              ),
              Text(
                '昵称',
                style: TextStyle(
                  color: theme.reversedPrimary,
                  fontSize: 14,
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              Container(
                width: Get.width,
                // padding: const EdgeInsets.symmetric(vertical: 9),
                decoration: BoxDecoration(
                    border: Border(
                        bottom: BorderSide(
                            width: 0.24,
                            color: TonyTheme.of(context).borderColor))),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: TextField(
                        controller: nameController,
                        style: TextStyle(
                          color: TonyTheme.of(context).onBackground,
                          fontSize: 16,
                          fontFamily: 'PingFang SC',
                          fontWeight: FontWeight.w500,
                        ),
                        decoration: const InputDecoration(
                          border: InputBorder.none, // 取消边框
                          filled: false, // 不填充背景色
                          hintText: '请输入你的昵称',
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 40,
              ),
              Text(
                'signature'.tr,
                style: TextStyle(
                  color: theme.reversedPrimary,
                  fontSize: 14,
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              Container(
                width: Get.width,
                // padding: const EdgeInsets.symmetric(vertical: 9),
                decoration: BoxDecoration(
                    border: Border(
                        bottom: BorderSide(
                            width: 0.24,
                            color: TonyTheme.of(context).borderColor))),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: TextField(
                        controller: whatsupController,
                        maxLength: 12,
                        style: TextStyle(
                          color: TonyTheme.of(context).onBackground,
                          fontSize: 16,
                          fontFamily: 'PingFang SC',
                          fontWeight: FontWeight.w500,
                        ),
                        decoration: InputDecoration(
                          border: InputBorder.none,
                          filled: false,
                          hintText: 'signature_hint'.tr,
                          counterStyle: const TextStyle(
                            color: Colors.grey,
                            fontSize: 12,
                          ),
                          counterText: '',
                          suffixIcon: Padding(
                            padding: const EdgeInsets.only(right: 0.0, top: 8),
                            child: Text(
                              '${whatsupController.text.length}/12',
                              style: const TextStyle(
                                color: Color(0xFF747474),
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 40,
              ),
              Text(
                '性别',
                style: TextStyle(
                  color: theme.reversedPrimary,
                  fontSize: 14,
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          gender = Gender.MALE;
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 8),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: theme.cardBackground,
                          border: Border.all(
                            color: gender == Gender.MALE
                                ? const Color(0xFF5F8BFF)
                                : Colors.transparent,
                            width: 2,
                          ),
                        ),
                        child: Row(
                          children: [
                            Image.asset(
                              'assets/male.png',
                              width: 24,
                            ),
                            const SizedBox(
                              width: 8,
                            ),
                            const Text(
                              '男',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                            Expanded(child: Container()),
                            Visibility(
                              visible: gender == Gender.MALE,
                              child: Image.asset(
                                'assets/check-one.png',
                                width: 24,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(
                    width: 15,
                  ),
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          gender = Gender.FEMALE;
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 8),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: theme.cardBackground,
                          border: Border.all(
                            color: gender == Gender.FEMALE
                                ? const Color(0xFFF36565)
                                : Colors.transparent,
                            width: 2,
                          ),
                        ),
                        child: Row(
                          children: [
                            Image.asset(
                              'assets/female.png',
                              width: 24,
                            ),
                            const SizedBox(
                              width: 8,
                            ),
                            const Text(
                              '女',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                            Expanded(child: Container()),
                            Visibility(
                              visible: gender == Gender.FEMALE,
                              child: Image.asset(
                                'assets/check-one.png',
                                width: 24,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              Expanded(child: Container()),
              TonyButton(
                text: '确认',
                onPressed: () async {
                  try {
                    final api = Get.find<ApiService>();
                    String? avatar;
                    if (avatarFile != null) {
                      final tokenRes = await api.file.getInstanceUploadToken();
                      final res =
                          await api.file.uploadFile(avatarFile!, tokenRes);

                      avatar =
                          'https://toooony-1324559352.cos.ap-guangzhou.myqcloud.com/${res.key}';

                      Get.log(avatar);
                    }

                    final newInfo = await api.auth.updateUserInfo(
                      nickname: nameController.text,
                      whatsup: whatsupController.text,
                      gender: gender,
                      avatar: avatar,
                    );

                    userController.user.value = newInfo;

                    Get.back();
                  } catch (e) {
                    Get.snackbar('error'.tr, 'modify_failed'.tr,
                        backgroundColor: theme.modalBackground,
                        colorText: Colors.white);
                  }
                },
              ),
              const SizedBox(
                height: 40,
              )
            ],
          ),
        ),
      ),
    );
  }
}
