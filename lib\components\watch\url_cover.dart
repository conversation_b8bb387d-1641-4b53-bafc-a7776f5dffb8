import 'package:Toooony/model/watch.dart';
import 'package:Toooony/theme/theme.dart';
import 'package:flutter/material.dart';

class URLCover extends StatelessWidget {
  final Color? color;

  const URLCover({super.key, required this.instance, this.color});

  final WatchInstance instance;

  @override
  Widget build(BuildContext context) {
    final theme = TonyTheme.of(context);
    var url = 'URL表盘';

    for (final param in instance.customFields) {
      if (param.key == 'url' && param.type == 'url') {
        try {
          var value = param.value;
          if (!value.startsWith('http://') && !value.startsWith('https://')) {
            value = 'https://$value';
          }
          final uri = Uri.parse(value);
          url = uri.host;
        } catch (e) {
          url = param.value;
        }
      }
    }

    return Container(
      width: 100,
      height: 100,
      margin: const EdgeInsets.only(right: 12),
      padding: const EdgeInsets.symmetric(horizontal: 10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(999),
        color: color ?? theme.cardBackground,
      ),
      child: Center(
        child: Text(
          url,
          textAlign: TextAlign.center,
          overflow: TextOverflow.ellipsis,
          maxLines: 2,
          style: TextStyle(color: theme.priamry, fontSize: 14),
        ),
      ),
    );
  }
}
