import 'package:Toooony/theme/theme.dart';
import 'package:card_swiper/card_swiper.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

final List<String> images = [
  'assets/shop_2.png',
  'assets/shop_3.png',
  'assets/shop_4.png',
];

final List<String> names = [
  'hip_hop_singer'.tr,
  'western_cowboy'.tr,
  'astronaut'.tr,
];

class TontShopPage extends StatefulWidget {
  const TontShopPage({super.key});

  @override
  State<TontShopPage> createState() => _TontShopPageState();
}

class _TontShopPageState extends State<TontShopPage> {
  
  Widget carousel(final BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(16),
      child: SizedBox(
        width: Get.width,
        height: Get.width * 0.6,
        child: Swiper(
          autoplay: true,
          itemBuilder: (BuildContext context, int index) {
            return Image.asset(
              "assets/shop_1.png",
              fit: BoxFit.cover,
            );
          },
          autoplayDelay: 3000,
          itemCount: 3,
          pagination: const SwiperPagination(
            builder: RectSwiperPaginationBuilder(
                size: Size(20, 8),
                activeSize: Size(20, 8),
                color: Colors.grey,
                activeColor: Colors.white),
          ),
        ),
      ),
    );
  }

  Widget shopContent(final BuildContext context) {
    return SliverToBoxAdapter(
      child: Column(
        children: [
          const SizedBox(
            height: 24,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'toooony_hat'.tr,
                style: const TextStyle(
                  color: Color(0xFFF3F3F3),
                  fontSize: 16,
                  fontFamily: 'PingFang SC',
                  fontWeight: FontWeight.w500,
                ),
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    'more'.tr,
                    style: const TextStyle(
                      color: Color(0xFF747474) /* 内容文字1 */,
                      fontSize: 14,
                      fontFamily: 'PingFang SC',
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  const Icon(
                    Icons.arrow_forward_ios,
                    size: 12,
                    color: Color(0xFF747474),
                  ),
                ],
              )
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = TonyTheme.of(context);
    return Scaffold(
      backgroundColor: theme.background,
      body: SafeArea(
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: 15,
          ),
          child: CustomScrollView(
            slivers: [
              SliverToBoxAdapter(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(
                      height: 24,
                    ),
                    Text(
                      'surrounding_shop'.tr,
                      style:const TextStyle(
                        color: Color(0xFFF3F3F3),
                        fontSize: 34,
                        fontFamily: 'PingFang SC',
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    carousel(context),
                  ],
                ),
              ),
              shopContent(context),
              SliverGrid(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  childAspectRatio: 0.65,
                  crossAxisCount: 2,
                  mainAxisSpacing: 15,
                  crossAxisSpacing: 15,
                ),
                delegate: SliverChildBuilderDelegate(
                  (BuildContext context, int index) {
                    return Container(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Image.asset(
                            images[index],
                            width: Get.width / 2,
                            height: Get.width / 2,
                            fit: BoxFit.cover,
                          ),
                          const SizedBox(
                            height: 8,
                          ),
                          Text(
                            names[index],
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontFamily: 'PingFang SC',
                            ),
                          ),
                          const SizedBox(
                            height: 8,
                          ),
                          const Text(
                            '¥199',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontFamily: 'D-DIN-PRO',
                              fontWeight: FontWeight.w600,
                            ),
                          )
                        ],
                      ),
                    );
                  },
                  childCount: 3,
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
